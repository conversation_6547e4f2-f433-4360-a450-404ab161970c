import request from '@/utils/request'

// 查询诊断列表
export function listDiagnosis(query) {
  return request({
    url: '/diagnosis/diagnosis/list',
    method: 'get',
    params: query
  })
}

// 查询诊断详细
export function getDiagnosis(id) {
  return request({
    url: '/diagnosis/diagnosis/' + id,
    method: 'get'
  })
}


// 新增诊断
export function addDiagnosis(data) {
  return request({
    url: '/diagnosis/diagnosis',
    method: 'post',
    data: data
  })
}

// 修改诊断
export function updateDiagnosis(data) {
  return request({
    url: '/diagnosis/diagnosis',
    method: 'put',
    data: data
  })
}

// 删除诊断
export function delDiagnosis(id) {
  return request({
    url: '/diagnosis/diagnosis/' + id,
    method: 'delete'
  })
}

// 导出诊断报告
export function exportDiagnosisReport(id) {
  return request({
    url: '/diagnosis/diagnosis/export/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 分享诊断链接
export function shareDiagnosis(data) {
  return request({
    url: '/diagnosis/diagnosis/share',
    method: 'post',
    data: data
  })
}

// 审核诊断
export function auditDiagnosis(id) {
  return request({
    url: '/diagnosis/diagnosis/audit/' + id,
    method: 'put'
  })
}

// 反审核诊断
export function unauditDiagnosis(id) {
  return request({
    url: '/diagnosis/diagnosis/unaudit/' + id,
    method: 'put'
  })
}

// 格式化报告数据
export function formatReportData(id) {
  return request({
    url: '/diagnosis/diagnosis/formatReportData/' + id,
    method: 'get'
  })
}

// 驳回诊断
export function rejectDiagnosis(data) {
  return request({
    url: '/diagnosis/diagnosis/reject',
    method: 'put',
    data: data
  })
}

// 检查新患者
export function checkNewPatients() {
  return request({
    url: '/diagnosis/diagnosis/check-new-patients',
    method: 'get'
  })
}

// 获取新患者提醒配置
export function getNotificationConfig() {
  return request({
    url: '/diagnosis/diagnosis/notification-config',
    method: 'get'
  })
}

// 更新新患者提醒配置
export function updateNotificationConfig(data) {
  return request({
    url: '/diagnosis/diagnosis/notification-config',
    method: 'post',
    data: data
  })
}

// 撤销审核
export function cancelAudit(id) {
  return request({
    url: '/diagnosis/diagnosis/cancel/' + id,
    method: 'put'
  })
}

// 提交审核
export function submitForAudit(id) {
  return request({
    url: '/diagnosis/diagnosis/submit/' + id,
    method: 'put'
  })
}

// 查询待审核诊断列表
export function listPendingReview(query) {
  return request({
    url: '/diagnosis/diagnosis/list',
    method: 'get',
    params: { ...query, status: '1' } // 状态为1表示待审核
  })
}

export function approveReview(data) {
  return request({
    url: '/diagnosis/diagnosis/approve',
    method: 'put',
    data: data
  })
}

export function rejectReview(data) {
  return request({
    url: '/diagnosis/diagnosis/reject',
    method: 'put',
    data: data
  })
}
