import request from '@/utils/request'

// 查询诊断记录列表
export function listDiagnosisRecord(query) {
  return request({
    url: '/diagnosis/record/list',
    method: 'get',
    params: query
  })
}

// 查询诊断记录详细
export function getDiagnosisRecord(id) {
  return request({
    url: '/diagnosis/record/' + id,
    method: 'get'
  })
}

// 根据诊断ID查询诊断记录列表
export function listDiagnosisRecordByDiagnosisId(diagnosisId) {
  return request({
    url: '/diagnosis/record/listByDiagnosisId/' + diagnosisId,
    method: 'get'
  })
}

// 新增诊断记录
export function addDiagnosisRecord(data) {
  return request({
    url: '/diagnosis/record',
    method: 'post',
    data: data
  })
}

// 修改诊断记录
export function updateDiagnosisRecord(data) {
  return request({
    url: '/diagnosis/record',
    method: 'put',
    data: data
  })
}

// 删除诊断记录
export function delDiagnosisRecord(id) {
  return request({
    url: '/diagnosis/record/' + id,
    method: 'delete'
  })
}

// 导出诊断记录
export function exportDiagnosisRecord(query) {
  return request({
    url: '/diagnosis/record/export',
    method: 'post',
    params: query
  })
}
