import { getToken } from '@/utils/auth'

/**
 * 全局唯一的 WebSocket 连接管理器
 * 替代Socket.IO，使用原生WebSocket与后端通信
 */
class GlobalWebSocketManager {
  constructor() {
    if (GlobalWebSocketManager.instance) {
      console.warn('GlobalWebSocketManager 已存在实例，返回现有实例')
      return GlobalWebSocketManager.instance
    }

    this.ws = null
    this.isConnected = false
    this.isConnecting = false
    this.connectionPromise = null
    this.eventBus = new EventTarget()
    this.messageQueue = [] // 离线消息队列
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 10
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.lastHeartbeat = Date.now()
    
    this.config = {
      url: import.meta.env.VITE_APP_WEBSOCKET_URL || 'ws://localhost:8080/ws/consultation',
      reconnectDelay: 1000,
      maxReconnectDelay: 30000,
      heartbeatInterval: 30000,
      heartbeatTimeout: 90000, // 增加到90秒，更宽松的超时设置
      messageTimeout: 10000,
      // 暂时禁用SockJS，使用原生WebSocket
      useSockJS: false,
      sockjsTransports: ['websocket', 'xhr-polling']
    }

    GlobalWebSocketManager.instance = this
    console.log('GlobalWebSocketManager 单例实例已创建')
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!GlobalWebSocketManager.instance) {
      new GlobalWebSocketManager()
    }
    return GlobalWebSocketManager.instance
  }

  /**
   * 建立WebSocket连接
   */
  async connect() {
    const connectionId = Date.now() + '-' + Math.random().toString(36).substr(2, 9)
    console.log(`[${connectionId}] 尝试建立 WebSocket 连接`)

    // 严格的状态检查
    if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      console.log(`[${connectionId}] WebSocket 已连接，直接返回`)
      return this.ws
    }

    if (this.isConnecting) {
      console.log(`[${connectionId}] WebSocket 正在连接中，等待完成`)
      return this.connectionPromise
    }

    this.isConnecting = true
    console.log(`[${connectionId}] 开始建立新的 WebSocket 连接`)

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        const token = getToken()
        if (!token) {
          console.error(`[${connectionId}] 未找到认证 token`)
          this.isConnecting = false
          reject(new Error('未找到认证token'))
          return
        }

        // 清理旧连接
        if (this.ws) {
          console.log(`[${connectionId}] 清理旧的 WebSocket 连接`)
          this.ws.close()
          this.ws = null
        }

        // 构建WebSocket URL - 使用原生WebSocket
        let wsUrl = `${this.config.url}?token=${encodeURIComponent(token)}`
        console.log(`[${connectionId}] 使用原生 WebSocket URL: ${wsUrl}`)

        // 创建新连接，添加超时控制
        this.ws = new WebSocket(wsUrl)
        
        // 设置连接超时
        const connectionTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            console.error(`[${connectionId}] WebSocket 连接超时`)
            this.ws.close()
            this.isConnecting = false
            this.connectionPromise = null
            reject(new Error('WebSocket连接超时'))
          }
        }, 10000) // 10秒超时

        // 连接成功处理
        this.ws.onopen = (event) => {
          clearTimeout(connectionTimeout)
          console.log(`[${connectionId}] WebSocket 连接成功`)
          this.isConnected = true
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.lastHeartbeat = Date.now()
          
          // 启动心跳
          this.startHeartbeat()
          
          // 处理离线消息队列
          this.processMessageQueue()
          
          this.emit('connected', { event })
          resolve(this.ws)
        }

        // 消息处理
        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data)
            console.log(`[${connectionId}] 收到 WebSocket 消息:`, message)
            this.handleMessage(message)
          } catch (error) {
            console.error(`[${connectionId}] 解析 WebSocket 消消息失败:`, error, event.data)
          }
        }

        // 连接断开处理
        this.ws.onclose = (event) => {
          clearTimeout(connectionTimeout)
          console.warn(`[${connectionId}] WebSocket 连接断开:`, {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
            url: wsUrl
          })
          
          this.isConnected = false
          this.isConnecting = false
          this.connectionPromise = null
          this.stopHeartbeat()
          
          // 详细的错误码分析
          let errorMessage = this.getCloseCodeMessage(event.code)
          console.error(`[${connectionId}] 连接断开原因: ${errorMessage}`)
          
          this.emit('disconnected', { 
            code: event.code, 
            reason: event.reason,
            message: errorMessage
          })
          
          // 自动重连（除非是正常关闭或认证失败）
          if (event.code !== 1000 && event.code !== 1008) {
            this.scheduleReconnect()
          }
        }

        // 连接错误处理
        this.ws.onerror = (error) => {
          clearTimeout(connectionTimeout)
          console.error(`[${connectionId}] WebSocket 连接错误:`, {
            error,
            url: wsUrl,
            readyState: this.ws?.readyState,
            token: token ? '已提供' : '未提供'
          })
          
          this.isConnected = false
          this.isConnecting = false
          this.connectionPromise = null
          
          this.emit('connect_error', { error, url: wsUrl })
          reject(new Error(`WebSocket连接失败: ${wsUrl}`))
        }

      } catch (error) {
        console.error(`[${connectionId}] WebSocket 连接异常:`, error)
        this.isConnecting = false
        this.connectionPromise = null
        reject(error)
      }
    })

    return this.connectionPromise
  }

  /**
   * 获取WebSocket关闭码的详细说明
   */
  getCloseCodeMessage(code) {
    const closeCodeMessages = {
      1000: '正常关闭',
      1001: '端点离开',
      1002: '协议错误',
      1003: '不支持的数据类型',
      1005: '没有收到状态码',
      1006: '异常断开连接（通常是网络问题或服务器未响应）',
      1007: '数据类型错误',
      1008: '违反策略（可能是认证失败）',
      1009: '消息过大',
      1010: '扩展协商失败',
      1011: '服务器错误',
      1012: '服务重启',
      1013: '稍后重试',
      1014: '错误的网关',
      1015: 'TLS握手失败'
    }
    return closeCodeMessages[code] || `未知错误码: ${code}`
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(message) {
    const { type, data } = message

    // 更新心跳时间 - 任何消息都表示连接活跃
    this.lastHeartbeat = Date.now()

    switch (type) {
      case 'connected':
        console.log('服务器确认连接成功')
        this.emit('server-connected', data)
        break
      case 'consultation-notification':
        this.emit('consultation-notification', data)
        break
      case 'consultation-broadcast':
        this.emit('consultation-broadcast', data)
        break
      case 'offline-notifications':
        this.emit('offline-notifications', data)
        break
      case 'heartbeat':
      case 'ping':
        this.handleHeartbeat(data)
        break
      case 'pong':
        console.debug('收到pong响应:', data)
        // pong消息表示服务器响应，更新心跳时间
        this.lastHeartbeat = Date.now()
        break
      default:
        console.log('未知消息类型:', type, data)
        this.emit(type, data)
    }
  }

  /**
   * 处理心跳消息
   */
  handleHeartbeat(data) {
    console.debug('收到心跳/ping消息:', data)
    // 发送心跳响应或pong
    this.send({
      type: 'pong',
      data: {
        timestamp: Date.now(),
        serverTimestamp: data?.timestamp
      }
    })
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat()
    
    // 初始化心跳时间
    this.lastHeartbeat = Date.now()
    console.log('启动心跳检测，间隔:', this.config.heartbeatInterval, 'ms')

    this.heartbeatTimer = setInterval(() => {
      const now = Date.now()
      const timeSinceLastHeartbeat = now - this.lastHeartbeat
      
      console.debug('心跳检查:', {
        timeSinceLastHeartbeat,
        timeout: this.config.heartbeatTimeout,
        connected: this.isConnected,
        readyState: this.ws?.readyState
      })

      // 如果连接状态正常，定期发送ping消息和检查心跳超时
      if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
        // 每个心跳间隔都发送ping消息保持连接活跃
        this.sendPing()

        // 检查是否心跳超时
        if (timeSinceLastHeartbeat > this.config.heartbeatTimeout) {
          console.warn('心跳超时，主动关闭连接触发重连')
          this.ws.close(1006, 'heartbeat timeout')
        }
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 发送ping消息保持连接活跃
   */
  sendPing() {
    if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      const pingMessage = {
        type: 'ping',
        data: {
          timestamp: Date.now()
        }
      }
      this.send(pingMessage)
      console.debug('发送ping消息保持连接活跃')
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连')
      this.emit('max_reconnect_attempts_reached')
      return
    }

    const delay = Math.min(
      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts),
      this.config.maxReconnectDelay
    )

    this.reconnectAttempts++
    console.log(`${delay}ms后进行第${this.reconnectAttempts}次重连`)

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败:', error)
      })
    }, delay)
  }

  /**
   * 发送消息
   */
  send(message) {
    if (!this.isConnected || this.ws?.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，消息加入队列:', message)
      this.messageQueue.push(message)
      return false
    }

    try {
      const jsonMessage = JSON.stringify(message)
      this.ws.send(jsonMessage)
      console.debug('发送WebSocket消息:', message)
      return true
    } catch (error) {
      console.error('发送WebSocket消息失败:', error)
      return false
    }
  }

  /**
   * 处理离线消息队列
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.send(message)
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    console.log('断开 WebSocket 连接')
    this.isConnecting = false
    this.connectionPromise = null
    this.stopHeartbeat()

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.ws) {
      this.ws.close(1000, 'manual disconnect')
      this.ws = null
    }

    this.isConnected = false
    this.emit('disconnected', { reason: 'manual' })
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 发送事件到事件总线
   */
  emit(eventName, data) {
    this.eventBus.dispatchEvent(new CustomEvent(eventName, { detail: data }))
  }

  /**
   * 监听事件
   */
  on(eventName, handler) {
    this.eventBus.addEventListener(eventName, (event) => {
      handler(event.detail)
    })
  }

  /**
   * 移除事件监听
   */
  off(eventName, handler) {
    this.eventBus.removeEventListener(eventName, handler)
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      readyState: this.ws?.readyState,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length
    }
  }

  /**
   * 发送通知确认
   */
  acknowledgeNotification(notificationId) {
    return this.send({
      type: 'notification-ack',
      data: {
        notificationId,
        timestamp: Date.now()
      }
    })
  }

  /**
   * 请求离线通知
   */
  requestOfflineNotifications() {
    return this.send({
      type: 'request-offline-notifications',
      data: {
        timestamp: Date.now()
      }
    })
  }
}

// 导出单例实例
const globalWebSocket = GlobalWebSocketManager.getInstance()

export default globalWebSocket
