<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; max-height: 300px; overflow-y: auto; }
        button { padding: 8px 16px; margin: 5px; }
        input { width: 300px; padding: 8px; margin: 5px; }
    </style>
</head>
<body>
    <h1>WebSocket连接诊断工具</h1>

    <div>
        <label>WebSocket URL:</label><br>
        <input type="text" id="wsUrl" value="wss://xgyyx.etkqrmyy.com/api/ws/consultation">
    </div>

    <div>
        <label>Token:</label><br>
        <input type="text" id="token" placeholder="请输入JWT Token">
    </div>

    <div>
        <button onclick="testConnection()">测试连接</button>
        <button onclick="disconnect()">断开连接</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div id="status" class="status warning">未连接</div>

    <div>
        <h3>连接日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let connectionStartTime = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            if (type === 'error') logEntry.style.color = 'red';
            if (type === 'success') logEntry.style.color = 'green';
            if (type === 'warning') logEntry.style.color = 'orange';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function testConnection() {
            const url = document.getElementById('wsUrl').value;
            const token = document.getElementById('token').value;

            if (!url) {
                log('请输入WebSocket URL', 'error');
                return;
            }

            if (ws) {
                ws.close();
            }

            connectionStartTime = Date.now();
            const finalUrl = token ? `${url}?token=${encodeURIComponent(token)}` : url;

            log(`开始连接: ${finalUrl}`, 'info');
            updateStatus('连接中...', 'warning');

            try {
                ws = new WebSocket(finalUrl);

                // 连接超时检测
                const timeout = setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        log('连接超时 (10秒)', 'error');
                        ws.close();
                    }
                }, 10000);

                ws.onopen = function(event) {
                    clearTimeout(timeout);
                    const connectTime = Date.now() - connectionStartTime;
                    log(`连接成功! 耗时: ${connectTime}ms`, 'success');
                    updateStatus('已连接', 'success');

                    // 发送测试消息
                    setTimeout(() => {
                        if (ws.readyState === WebSocket.OPEN) {
                            const testMessage = {
                                type: 'ping',
                                data: { timestamp: Date.now() }
                            };
                            ws.send(JSON.stringify(testMessage));
                            log('发送测试ping消息', 'info');
                        }
                    }, 1000);
                };

                ws.onmessage = function(event) {
                    log(`收到消息: ${event.data}`, 'success');
                };

                ws.onclose = function(event) {
                    clearTimeout(timeout);
                    const codes = {
                        1000: '正常关闭',
                        1001: '端点离开',
                        1002: '协议错误',
                        1003: '不支持的数据',
                        1005: '无状态码',
                        1006: '异常断开(网络/服务器问题)',
                        1007: '数据类型错误',
                        1008: '策略违反(认证失败)',
                        1009: '消息过大',
                        1010: '扩展失败',
                        1011: '服务器错误',
                        1015: 'TLS握手失败'
                    };

                    const reason = codes[event.code] || `未知错误码: ${event.code}`;
                    log(`连接关闭: ${event.code} - ${reason}`, 'error');
                    updateStatus(`连接关闭: ${reason}`, 'error');
                };

                ws.onerror = function(error) {
                    clearTimeout(timeout);
                    log(`连接错误: ${error}`, 'error');
                    updateStatus('连接错误', 'error');

                    // 额外的错误诊断
                    log('错误诊断:', 'warning');
                    log(`- URL: ${finalUrl}`, 'info');
                    log(`- 协议: ${url.startsWith('wss://') ? 'WSS (安全)' : 'WS (非安全)'}`, 'info');
                    log(`- Token: ${token ? '已提供' : '未提供'}`, 'info');

                    // 网络连通性测试建议
                    log('建议检查:', 'warning');
                    log('1. 服务器是否运行并监听WebSocket端口', 'info');
                    log('2. 防火墙/网络是否允许WebSocket连接', 'info');
                    log('3. SSL证书是否有效 (如果使用wss://)', 'info');
                    log('4. 服务器WebSocket端点路径是否正确', 'info');
                    log('5. Token是否有效且未过期', 'info');
                };

            } catch (error) {
                log(`创建WebSocket失败: ${error.message}`, 'error');
                updateStatus('创建失败', 'error');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close(1000, 'manual disconnect');
                log('手动断开连接', 'info');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('WebSocket连接测试工具已加载', 'info');
            log('请输入WebSocket URL和Token（如果需要），然后点击测试连接', 'info');
        };
    </script>
</body>
</html>

