# 诊断模块API权限分析文档

## 概述

本文档详细分析了PACS系统诊断模块前端API请求对应的后端权限控制代码，包括所有Controller类中的权限注解配置。

## 权限控制机制

系统使用Spring Security的`@PreAuthorize`注解进行权限控制，权限检查通过`@ss.hasPermi('权限标识')`表达式实现。

## 1. 诊断管理模块 (DiagnosisController)

### API文件：`pacs-admin-ui/src/api/diagnosis/diagnosis.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/DiagnosisController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `listDiagnosis` | `/diagnosis/diagnosis/list` | GET | `diagnosis:diagnosis:list` | 查询诊断列表 |
| `getDiagnosis` | `/diagnosis/diagnosis/{id}` | GET | `diagnosis:diagnosis:query` | 查询诊断详情 |
| `getLatestDiagnosis` | `/diagnosis/diagnosis/latest/{studyId}` | GET | `diagnosis:diagnosis:query` | 查询最新诊断 |
| `addDiagnosis` | `/diagnosis/diagnosis` | POST | `diagnosis:diagnosis:add` | 新增诊断 |
| `updateDiagnosis` | `/diagnosis/diagnosis` | PUT | `diagnosis:diagnosis:edit` | 修改诊断 |
| `delDiagnosis` | `/diagnosis/diagnosis/{id}` | DELETE | `diagnosis:diagnosis:remove` | 删除诊断 |
| `exportDiagnosisReport` | `/diagnosis/diagnosis/export/{id}` | GET | `diagnosis:diagnosis:query` | 导出诊断报告 |
| `shareDiagnosis` | `/diagnosis/diagnosis/share` | POST | `diagnosis:diagnosis:edit` | 分享诊断链接 |
| `auditDiagnosis` | `/diagnosis/diagnosis/audit/{id}` | PUT | `diagnosis:diagnosis:audit` | 审核诊断 |
| `unauditDiagnosis` | `/diagnosis/diagnosis/unaudit/{id}` | PUT | `diagnosis:diagnosis:audit` | 反审核诊断 |
| `formatReportData` | `/diagnosis/diagnosis/formatReportData/{id}` | GET | `diagnosis:diagnosis:query` | 格式化报告数据 |
| `getHospitalLogo` | `/system/config/hospitalLogo` | GET | 无权限控制 | 获取医院Logo |
| `rejectDiagnosis` | `/diagnosis/diagnosis/reject` | PUT | `diagnosis:diagnosis:audit` | 驳回诊断 |
| `checkNewPatients` | `/diagnosis/diagnosis/check-new-patients` | GET | `diagnosis:diagnosis:list` | 检查新患者 |
| `getNotificationConfig` | `/diagnosis/diagnosis/notification-config` | GET | `diagnosis:diagnosis:list` | 获取通知配置 |
| `updateNotificationConfig` | `/diagnosis/diagnosis/notification-config` | POST | `diagnosis:diagnosis:edit` | 更新通知配置 |
| `cancelAudit` | `/diagnosis/diagnosis/cancel/{id}` | PUT | `diagnosis:diagnosis:audit` | 撤销审核 |
| `submitForAudit` | `/diagnosis/diagnosis/submit/{id}` | PUT | `diagnosis:diagnosis:edit` | 提交审核 |
| `listPendingReview` | `/diagnosis/diagnosis/list` | GET | `diagnosis:diagnosis:list` | 查询待审核列表 |
| `approveReview` | `/diagnosis/diagnosis/approve` | PUT | `diagnosis:diagnosis:audit` | 审核通过 |
| `rejectReview` | `/diagnosis/diagnosis/reject` | PUT | `diagnosis:diagnosis:audit` | 审核驳回 |

## 2. 诊断配置模块 (DiagnosisConfigController)

### API文件：`pacs-admin-ui/src/api/diagnosis/config.js`
### 后端控制器：未找到对应Controller，可能为系统配置模块

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `getDiagnosisConfig` | `/diagnosis/config/list` | GET | 待确认 | 获取诊断配置 |
| `updateDiagnosisConfig` | `/diagnosis/config/update` | PUT | 待确认 | 更新诊断配置 |
| `resetDiagnosisConfig` | `/diagnosis/config/reset` | POST | 待确认 | 重置诊断配置 |

## 3. 诊断记录模块 (DiagnosisRecordController)

### API文件：`pacs-admin-ui/src/api/diagnosis/diagnosisRecord.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/DiagnosisRecordController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `listDiagnosisRecord` | `/diagnosis/record/list` | GET | `diagnosis:record:list` | 查询诊断记录列表 |
| `getDiagnosisRecord` | `/diagnosis/record/{id}` | GET | `diagnosis:record:query` | 查询诊断记录详情 |
| `listDiagnosisRecordByDiagnosisId` | `/diagnosis/record/listByDiagnosisId/{diagnosisId}` | GET | `diagnosis:record:list` | 根据诊断ID查询记录 |
| `addDiagnosisRecord` | `/diagnosis/record` | POST | `diagnosis:record:add` | 新增诊断记录 |
| `updateDiagnosisRecord` | `/diagnosis/record` | PUT | `diagnosis:record:edit` | 修改诊断记录 |
| `delDiagnosisRecord` | `/diagnosis/record/{id}` | DELETE | `diagnosis:record:remove` | 删除诊断记录 |
| `exportDiagnosisRecord` | `/diagnosis/record/export` | POST | `diagnosis:record:export` | 导出诊断记录 |

## 4. 诊断字典树模块 (DiagnosisDictController)

### API文件：`pacs-admin-ui/src/api/diagnosis/diagnosisTree.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/DiagnosisDictController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `getDiagnosisTree` | `/diagnosis/dict/tree` | GET | `diagnosis:dict:list` | 查询诊断字典树 |
| `getDiagnosisByCategory` | `/diagnosis/dict/list` | GET | `diagnosis:dict:list` | 根据分类查询字典 |
| `getAllDiagnosisDicts` | `/diagnosis/dict/list` | GET | `diagnosis:dict:list` | 查询所有启用字典 |
| `searchDiagnosisDicts` | `/diagnosis/dict/list` | GET | `diagnosis:dict:list` | 搜索诊断字典 |

## 5. 诊断字典模块 (DiagnosisDictController)

### API文件：`pacs-admin-ui/src/api/diagnosis/dict.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/DiagnosisDictController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `listDict` | `/diagnosis/dict/list` | GET | `diagnosis:dict:list` | 查询诊断字典列表 |
| `getDict` | `/diagnosis/dict/{id}` | GET | `diagnosis:dict:query` | 查询诊断字典详情 |
| `addDict` | `/diagnosis/dict` | POST | `diagnosis:dict:add` | 新增诊断字典 |
| `updateDict` | `/diagnosis/dict` | PUT | `diagnosis:dict:edit` | 修改诊断字典 |
| `delDict` | `/diagnosis/dict/{id}` | DELETE | `diagnosis:dict:remove` | 删除诊断字典 |

## 6. 诊断字典分类模块 (DiagnoseDictCategoryController)

### API文件：`pacs-admin-ui/src/api/diagnosis/dictCategory.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/DiagnoseDictCategoryController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `listDictCategory` | `/diagnosis/dictCategory/list` | GET | `diagnosis:dictCategory:list` | 查询字典分类列表 |
| `getDictCategory` | `/diagnosis/dictCategory/{id}` | GET | `diagnosis:dictCategory:query` | 查询字典分类详情 |
| `addDictCategory` | `/diagnosis/dictCategory` | POST | `diagnosis:dictCategory:add` | 新增字典分类 |
| `updateDictCategory` | `/diagnosis/dictCategory` | PUT | `diagnosis:dictCategory:edit` | 修改字典分类 |
| `delDictCategory` | `/diagnosis/dictCategory/{id}` | DELETE | `diagnosis:dictCategory:remove` | 删除字典分类 |

## 7. PDF生成模块 (DiagnosisPdfController)

### API文件：`pacs-admin-ui/src/api/diagnosis/pdf.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/DiagnosisPdfController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `generateBatchPdf` | `/diagnosis/pdf/generateBatch` | POST | `diagnosis:pdf:generate` | 批量生成PDF |
| `generatePdf` | `/diagnosis/pdf/generate/{id}` | POST | `diagnosis:pdf:generate` | 生成指定PDF |
| `generateBatchPdfAsync` | `/diagnosis/pdf/generateBatchAsync` | POST | `diagnosis:pdf:generate` | 异步批量生成PDF |
| `getPdfStatistics` | `/diagnosis/pdf/statistics` | GET | `diagnosis:pdf:generate` | 获取PDF统计信息 |
| `getPdfStatusList` | `/diagnosis/pdf/statusList` | GET | `diagnosis:pdf:generate` | 获取PDF状态列表 |

## 8. 诊断记录模块 (DiagnosisRecordController)

### API文件：`pacs-admin-ui/src/api/diagnosis/record.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/DiagnosisRecordController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `listRecord` | `/diagnosis/record/list` | GET | `diagnosis:record:list` | 查询诊断记录列表 |
| `getRecord` | `/diagnosis/record/{id}` | GET | `diagnosis:record:query` | 查询诊断记录详情 |
| `listRecordByDiagnosisId` | `/diagnosis/record/listByDiagnosisId/{diagnosisId}` | GET | `diagnosis:record:list` | 根据诊断ID查询记录 |
| `addRecord` | `/diagnosis/record` | POST | `diagnosis:record:add` | 新增诊断记录 |
| `updateRecord` | `/diagnosis/record` | PUT | `diagnosis:record:edit` | 修改诊断记录 |
| `delRecord` | `/diagnosis/record/{id}` | DELETE | `diagnosis:record:remove` | 删除诊断记录 |

## 9. 报告模板模块 (ReportTemplateController)

### API文件：`pacs-admin-ui/src/api/diagnosis/reportTemplate.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/ReportTemplateController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `listReportTemplate` | `/diagnosis/reportTemplate/list` | GET | `diagnosis:reportTemplate:list` | 查询报告模板列表 |
| `getReportTemplate` | `/diagnosis/reportTemplate/{id}` | GET | 无权限控制 | 查询报告模板详情 |
| `addReportTemplate` | `/diagnosis/reportTemplate` | POST | 无权限控制 | 新增报告模板 |
| `updateReportTemplate` | `/diagnosis/reportTemplate` | PUT | 无权限控制 | 修改报告模板 |
| `delReportTemplate` | `/diagnosis/reportTemplate/{id}` | DELETE | 无权限控制 | 删除报告模板 |
| `listTemplatesByModality` | `/diagnosis/reportTemplate/listByModality` | GET | 无权限控制 | 根据检查类型查询模板 |
| `listTemplatesByModalityAndBodyPart` | `/diagnosis/reportTemplate/listByModalityAndBodyPart` | GET | 无权限控制 | 根据检查类型和部位查询 |
| `getApplicableTemplate` | `/diagnosis/reportTemplate/getApplicableTemplate` | GET | 无权限控制 | 获取适用模板 |
| `getDefaultTemplate` | `/diagnosis/reportTemplate/getDefaultTemplate` | GET | 无权限控制 | 获取默认模板 |
| `setDefaultTemplate` | `/diagnosis/reportTemplate/setDefault/{id}` | PUT | 无权限控制 | 设置默认模板 |
| `listEnabledTemplates` | `/diagnosis/reportTemplate/listEnabled` | GET | 无权限控制 | 查询启用模板 |
| `validateJson` | `/diagnosis/reportTemplate/validateJson` | POST | 无权限控制 | 验证JSON格式 |

## 10. 模板管理模块 (TemplateController)

### API文件：`pacs-admin-ui/src/api/diagnosis/template.js`
### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/TemplateController.java`

| 前端API方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|------------|----------|----------|----------|----------|
| `listTemplate` | `/diagnosis/template/list` | GET | `diagnosis:template:list` | 查询模板列表 |
| `getTemplate` | `/diagnosis/template/{id}` | GET | `diagnosis:template:query` | 查询模板详情 |
| `addTemplate` | `/diagnosis/template` | POST | `diagnosis:template:add` | 新增模板 |
| `updateTemplate` | `/diagnosis/template` | PUT | `diagnosis:template:edit` | 修改模板 |
| `delTemplate` | `/diagnosis/template/{id}` | DELETE | `diagnosis:template:remove` | 删除模板 |
| `getUserTemplates` | `/diagnosis/template/user` | GET | 无权限控制 | 获取用户模板 |
| `getPublicTemplates` | `/diagnosis/template/public` | GET | 无权限控制 | 获取公共模板 |
| `getTemplatesByModalityType` | `/diagnosis/template/modality/{modalityType}` | GET | 无权限控制 | 根据检查类型查询 |
| `searchTemplates` | `/diagnosis/template/search` | GET | 无权限控制 | 搜索模板 |
| `useTemplate` | `/diagnosis/template/use/{id}` | POST | 无权限控制 | 使用模板 |
| `getDefaultTemplate` | `/diagnosis/template/default` | GET | 无权限控制 | 获取默认模板 |

## 11. PDF配置管理模块 (DiagnosisPdfConfigController)

### 后端控制器：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/DiagnosisPdfConfigController.java`

| 后端方法 | 请求路径 | HTTP方法 | 权限标识 | 权限说明 |
|----------|----------|----------|----------|----------|
| `getConfig` | `/diagnosis/pdf/config/list` | GET | `diagnosis:pdf:config` | 获取PDF配置 |
| `updateConfig` | `/diagnosis/pdf/config/update` | PUT | `diagnosis:pdf:config` | 更新PDF配置 |
| `resetConfig` | `/diagnosis/pdf/config/reset` | POST | `diagnosis:pdf:config` | 重置PDF配置 |
| `testConfig` | `/diagnosis/pdf/config/test` | POST | `diagnosis:pdf:config` | 测试配置连通性 |

## 权限标识汇总

### 诊断管理权限
- `diagnosis:diagnosis:list` - 查询诊断列表
- `diagnosis:diagnosis:query` - 查询诊断详情
- `diagnosis:diagnosis:add` - 新增诊断
- `diagnosis:diagnosis:edit` - 编辑诊断
- `diagnosis:diagnosis:remove` - 删除诊断
- `diagnosis:diagnosis:audit` - 审核诊断

### 诊断记录权限
- `diagnosis:record:list` - 查询诊断记录列表
- `diagnosis:record:query` - 查询诊断记录详情
- `diagnosis:record:add` - 新增诊断记录
- `diagnosis:record:edit` - 修改诊断记录
- `diagnosis:record:remove` - 删除诊断记录
- `diagnosis:record:export` - 导出诊断记录

### 诊断字典权限
- `diagnosis:dict:list` - 查询诊断字典列表
- `diagnosis:dict:query` - 查询诊断字典详情
- `diagnosis:dict:add` - 新增诊断字典
- `diagnosis:dict:edit` - 修改诊断字典
- `diagnosis:dict:remove` - 删除诊断字典
- `diagnosis:dict:export` - 导出诊断字典

### 字典分类权限
- `diagnosis:dictCategory:list` - 查询字典分类列表
- `diagnosis:dictCategory:query` - 查询字典分类详情
- `diagnosis:dictCategory:add` - 新增字典分类
- `diagnosis:dictCategory:edit` - 修改字典分类
- `diagnosis:dictCategory:remove` - 删除字典分类
- `diagnosis:dictCategory:export` - 导出字典分类

### 模板管理权限
- `diagnosis:template:list` - 查询模板列表
- `diagnosis:template:query` - 查询模板详情
- `diagnosis:template:add` - 新增模板
- `diagnosis:template:edit` - 修改模板
- `diagnosis:template:remove` - 删除模板
- `diagnosis:template:export` - 导出模板

### 报告模板权限
- `diagnosis:reportTemplate:list` - 查询报告模板列表
- `diagnosis:reportTemplate:query` - 查询报告模板详情（已注释）
- `diagnosis:reportTemplate:add` - 新增报告模板（已注释）
- `diagnosis:reportTemplate:edit` - 修改报告模板（已注释）
- `diagnosis:reportTemplate:remove` - 删除报告模板（已注释）
- `diagnosis:reportTemplate:export` - 导出报告模板（已注释）

### PDF生成权限
- `diagnosis:pdf:generate` - PDF生成相关操作
- `diagnosis:pdf:config` - PDF配置管理

## 权限控制特点

### 1. 严格权限控制
- 核心业务操作（增删改查、审核）都有严格的权限控制
- 使用Spring Security的`@PreAuthorize`注解进行方法级权限控制
- 权限检查通过`@ss.hasPermi('权限标识')`表达式实现

### 2. 权限分级
- **查询权限**：`list`、`query` - 数据查看权限
- **操作权限**：`add`、`edit`、`remove` - 数据操作权限
- **审核权限**：`audit` - 业务审核权限
- **导出权限**：`export` - 数据导出权限
- **配置权限**：`config` - 系统配置权限

### 3. 部分开放接口
- 模板相关的查询接口多数无权限控制，便于业务使用
- 报告模板的大部分操作接口无权限控制（权限注解已注释）
- 字典树查询等公共接口无权限限制

### 4. 权限一致性
- 前端API调用与后端权限标识保持一致
- 权限标识遵循`模块:子模块:操作`的命名规范
- 权限标识采用冒号分隔的三级结构

### 5. 特殊权限处理
- 审核相关操作（审核、反审核、撤销审核、驳回）统一使用`audit`权限
- 提交审核操作使用`edit`权限而非`audit`权限
- PDF生成和配置管理使用独立的权限标识

## 权限缺失分析

### 1. 报告模板模块权限缺失
- `getReportTemplate`、`addReportTemplate`、`updateReportTemplate`、`delReportTemplate`等方法的权限注解被注释
- 建议恢复这些权限控制以确保数据安全

### 2. 诊断配置模块
- 前端有配置相关API但未找到对应的后端Controller
- 可能通过系统配置模块统一管理

### 3. 模板使用权限
- 模板使用、搜索等功能性接口无权限控制
- 考虑到业务便利性，这种设计是合理的

## 安全建议

1. **补充权限控制**：
   - 恢复报告模板模块的权限注解
   - 确认诊断配置模块的权限实现

2. **权限测试**：
   - 创建不同权限级别的测试用户
   - 验证权限控制的有效性
   - 测试权限边界情况

3. **权限审计**：
   - 定期检查权限配置的完整性
   - 监控权限使用情况
   - 记录权限变更日志

4. **文档维护**：
   - 在系统管理中维护权限说明
   - 更新用户手册中的权限说明
   - 建立权限变更审批流程

## 权限映射关系表

### 前端API与后端权限对应关系

| 前端API文件 | 后端Controller | 主要权限前缀 | 权限控制状态 |
|-------------|----------------|--------------|--------------|
| `diagnosis.js` | `DiagnosisController` | `diagnosis:diagnosis:*` | ✅ 完整控制 |
| `diagnosisRecord.js` | `DiagnosisRecordController` | `diagnosis:record:*` | ✅ 完整控制 |
| `record.js` | `DiagnosisRecordController` | `diagnosis:record:*` | ✅ 完整控制 |
| `dict.js` | `DiagnosisDictController` | `diagnosis:dict:*` | ✅ 完整控制 |
| `diagnosisTree.js` | `DiagnosisDictController` | `diagnosis:dict:*` | ✅ 完整控制 |
| `dictCategory.js` | `DiagnoseDictCategoryController` | `diagnosis:dictCategory:*` | ✅ 完整控制 |
| `template.js` | `TemplateController` | `diagnosis:template:*` | ⚠️ 部分控制 |
| `reportTemplate.js` | `ReportTemplateController` | `diagnosis:reportTemplate:*` | ⚠️ 权限已注释 |
| `pdf.js` | `DiagnosisPdfController` | `diagnosis:pdf:*` | ✅ 完整控制 |
| `config.js` | 未找到对应Controller | - | ❌ 无对应后端 |

## 权限控制统计

### 权限控制完整性统计
- **完全控制**：6个模块（诊断、记录、字典、分类、PDF生成、PDF配置）
- **部分控制**：2个模块（模板、报告模板）
- **无权限控制**：1个模块（诊断配置）

### 权限标识统计
- **诊断管理**：6个权限标识
- **诊断记录**：6个权限标识
- **诊断字典**：6个权限标识
- **字典分类**：6个权限标识
- **模板管理**：6个权限标识
- **报告模板**：6个权限标识（多数已注释）
- **PDF相关**：2个权限标识

**总计**：约38个权限标识

## 相关文件

### 前端文件
- **API接口文件**：`pacs-admin-ui/src/api/diagnosis/`目录下所有文件
- **权限工具**：`pacs-admin-ui/src/utils/permission.js`
- **权限检查组件**：各Vue组件中的权限控制逻辑

### 后端文件
- **控制器文件**：`ruoyi-admin/src/main/java/com/ruoyi/diagnosis/controller/`目录下所有Controller
- **权限服务**：`ruoyi-framework/src/main/java/com/ruoyi/framework/web/service/PermissionService.java`
- **权限配置**：`sql/诊断和会诊权限整理SQL.sql`

### 配置文件
- **权限菜单配置**：数据库`sys_menu`表
- **角色权限配置**：数据库`sys_role_menu`表
- **用户权限配置**：数据库`sys_user_role`表

---

*文档生成时间：2025-08-06*
*分析范围：诊断模块所有API接口权限控制*
*版本：v1.0*
