# 完整的 nginx 配置文件 - 包含 WebSocket 支持
# 解决 WebSocket 通过 nginx 反向代理的连接问题

worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    # 定义后端服务器组
    upstream backend {
        server localhost:8080;
        keepalive 32;
    }

    # WebSocket 升级支持的 map 配置
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    # 90 端口配置
    server {
        listen       90;
        server_name  localhost;

        location /admin {
            root   admin;
            index  index.html index.htm;
        }

        location /report/ {
            root           report;
            add_header 'Access-Control-Allow-Origin' '*';
        }

        # API 代理 - 增加 WebSocket 支持
        location /api/ {
            proxy_pass http://backend/api/;
            proxy_http_version 1.1;

            # 基本代理头
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket 支持的关键头
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;

            # WebSocket 超时设置
            proxy_connect_timeout 4s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 专门的 WebSocket 代理配置
        location /api/ws/ {
            proxy_pass http://backend/api/ws/;
            proxy_http_version 1.1;

            # WebSocket 协议升级必需的头
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket 特殊超时设置
            proxy_connect_timeout 7s;
            proxy_send_timeout 300s;  # 5分钟
            proxy_read_timeout 300s;  # 5分钟

            # 禁用缓冲，确保实时性
            proxy_buffering off;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

    # 8888 端口配置 - 生产环境
    server {
        listen       8888;
        server_name  eqq.natapp4.cc xgyyx.etkqrmyy.com;

        # 管理后台配置
        location /admin/ {
            alias E:/dcm/nginx-1.21.6/admin/;
            index index.html index.htm;
            try_files $uri $uri/ /admin/index.html;
        }

        location = /admin {
            return 301 /admin/;
        }

        location / {
            root   html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # API 代理 - 增加 WebSocket 支持
        location /api/ {
            proxy_pass http://backend/api/;
            proxy_http_version 1.1;

            # 基本代理头
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket 支持
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;

            # 超时设置
            proxy_connect_timeout 4s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 专门的 WebSocket 代理 - 关键配置
        location /api/ws/ {
            proxy_pass http://backend/api/ws/;
            proxy_http_version 1.1;

            # WebSocket 必需的协议升级头
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket 长连接超时设置
            proxy_connect_timeout 7s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;

            # 禁用缓冲以确保实时通信
            proxy_buffering off;
            proxy_cache off;

            # 支持较大的消息
            proxy_max_temp_file_size 0;
        }

        # DCM4CHEE 代理
        location /dcm4chee-arc/ {
            proxy_pass http://localhost:8088/dcm4chee-arc/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        }
    }

    # SSL 配置示例（如果使用 HTTPS）
    # server {
    #     listen       443 ssl;
    #     server_name  xgyyx.etkqrmyy.com;
    #
    #     ssl_certificate      /path/to/your/cert.pem;
    #     ssl_certificate_key  /path/to/your/private.key;
    #
    #     # 其他配置与上面相同，但需要注意：
    #     # proxy_set_header X-Forwarded-Proto https;
    # }
}
