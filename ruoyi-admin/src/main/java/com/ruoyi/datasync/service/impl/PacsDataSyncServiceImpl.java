package com.ruoyi.datasync.service.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.OssFileUtils;
import com.ruoyi.datasync.domain.PacsPatientStudy;
import com.ruoyi.datasync.domain.PacsReport;
import com.ruoyi.datasync.domain.PacsSyncStatus;
import com.ruoyi.datasync.mapper.PacsSyncStatusMapper;
import com.ruoyi.datasync.mapper.PacsPatientStudyMapper;
import com.ruoyi.datasync.mapper.PacsReportMapper;
import com.ruoyi.datasync.mapper.PacsSourceDataMapper;
import com.ruoyi.datasync.service.PacsDataSyncService;
import com.ruoyi.diagnosis.domain.Diagnosis;
import com.ruoyi.diagnosis.mapper.DiagnosisMapper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * PACS数据同步服务实现类
 */
@Service
public class PacsDataSyncServiceImpl implements PacsDataSyncService {

    private static final Logger log = LoggerFactory.getLogger(PacsDataSyncServiceImpl.class);

    // 同步类型常量
    private static final String SYNC_TYPE_STUDY = "pacs_study";
    private static final String SYNC_TYPE_REPORT = "pacs_report";


    @Autowired
    private PacsSyncStatusMapper pacsSyncStatusMapper;

    @Autowired
    private PacsPatientStudyMapper pacsPatientStudyMapper;

    @Autowired
    private PacsReportMapper pacsReportMapper;

    @Autowired
    private PacsSourceDataMapper pacsSourceDataMapper;

    @Autowired
    private OssFileUtils ossFileUtils;

    @Resource(name = "pacsJdbcTemplate")
    private JdbcTemplate pacsJdbcTemplate;

    @Resource
    private JdbcTemplate mainJdbcTemplate;

    @Resource
    private DiagnosisMapper diagnosisMapper;

    /**
     * 初始化或获取同步时间
     */
    private Date getLastSyncTime(String syncType) {

        try {
            Date lastSyncTime = null;

            if (SYNC_TYPE_STUDY.equals(syncType)) {
                // 从检查数据表获取最新时间
                lastSyncTime = mainJdbcTemplate.queryForObject(
                        "SELECT MAX(check_finish_time) FROM pacs_patient_study", Date.class);
            } else if (SYNC_TYPE_REPORT.equals(syncType)) {
                // 从报告数据表获取最新时间
                lastSyncTime = mainJdbcTemplate.queryForObject(
                        "SELECT MAX(last_audit_time) FROM pacs_report", Date.class);
            }

            if (lastSyncTime != null) {
                return lastSyncTime;
            }
        } catch (Exception e) {
            log.warn("获取最后同步时间失败，使用默认时间: {}", e.getMessage());
        }

        // 默认同步30天内的数据
        LocalDateTime defaultTime = LocalDateTime.now().minusYears(30);
        return Date.from(defaultTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 更新同步状态
     */
    private void updateSyncStatus(String syncType, Date syncTime, String status, Integer insertCount, Integer updateCount, String remark) {
        PacsSyncStatus syncStatus = pacsSyncStatusMapper.selectByType(syncType);
        if (syncStatus == null) {
            // 创建新的状态记录
            syncStatus = new PacsSyncStatus();
            syncStatus.setSyncType(syncType);
            syncStatus.setLastSyncTime(syncTime);
            syncStatus.setSyncStatus(status);
            syncStatus.setInsertCount(Long.valueOf(insertCount));
            syncStatus.setUpdateCount(Long.valueOf(updateCount));
            syncStatus.setRemark(remark);
            syncStatus.setCreateBy("system");
            pacsSyncStatusMapper.insertPacsSyncStatus(syncStatus);
        } else {
            // 更新已有状态
            syncStatus.setLastSyncTime(syncTime);
            syncStatus.setSyncStatus(status);
            syncStatus.setInsertCount(Long.valueOf(insertCount));
            syncStatus.setUpdateCount(Long.valueOf(updateCount));
            syncStatus.setRemark(remark);
            syncStatus.setUpdateBy("system");
            pacsSyncStatusMapper.updatePacsSyncStatus(syncStatus);
        }
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public int syncPacsPatientStudy() {
        Date startTime = new Date();
        Date lastSyncTime = null;
        int totalProcessed = 0;
        int insertCount = 0;
        int updateCount = 0;
        int errorCount = 0;
        List<String> errorDetails = new ArrayList<>();

        try {
            log.info("开始PACS检查数据同步任务...");

            // 1. 获取上次同步时间
            lastSyncTime = getLastSyncTime(SYNC_TYPE_STUDY);
            log.info("上次同步时间: {}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(lastSyncTime));

            // 2. 从PACS数据源获取数据
            List<PacsPatientStudy> pacsPatientStudyList;
            try {
                pacsPatientStudyList = getPatientStudyFromPacs(lastSyncTime);
                log.info("从PACS数据源获取到 {} 条记录", pacsPatientStudyList.size());
            } catch (Exception e) {
                String errorMsg = "从PACS数据源获取数据失败: " + e.getMessage();
                log.error(errorMsg, e);
                updateSyncStatus(SYNC_TYPE_STUDY, new Date(), "失败", 0, 0, errorMsg);
                throw new RuntimeException(errorMsg, e);
            }

            if (pacsPatientStudyList.isEmpty()) {
                String message = "没有新数据需要同步";
                updateSyncStatus(SYNC_TYPE_STUDY, new Date(), "成功", 0, 0, message);
                log.info(message);
                return 0;
            }

            //获取系统参数影像数据来源
            String dicomSyncFlag = determineDicomSyncFlag();

            // 3. 逐条处理数据
            for (int i = 0; i < pacsPatientStudyList.size(); i++) {
                PacsPatientStudy pacsPatientStudy = pacsPatientStudyList.get(i);
                pacsPatientStudy.setDicomSyncFlag(dicomSyncFlag);

                totalProcessed++;

                try {
                    // 数据验证
                    if (!validatePatientStudyData(pacsPatientStudy)) {
                        errorCount++;
                        String error = String.format("数据验证失败 - 患者: %s, 检查号: %s", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode());
                        errorDetails.add(error);
                        log.warn(error);
                        continue;
                    }

                    // 检查MySQL中是否已存在该记录
                    PacsPatientStudy existingStudy = null;
                    try {
                        existingStudy = pacsPatientStudyMapper.selectPacsPatientStudyByKey(pacsPatientStudy.getHospitalId(), pacsPatientStudy.getExamCode());
                    } catch (Exception e) {
                        errorCount++;
                        String error = String.format("查询现有记录失败 - 检查号: %s, 错误: %s", pacsPatientStudy.getExamCode(), e.getMessage());
                        errorDetails.add(error);
                        log.error(error, e);
                        continue;
                    }

                    // 设置诊断状态：如果有报告数据（影像所见或影像意见），则设置为院内诊断
                    boolean hasReportData = StringUtils.isNotEmpty(pacsPatientStudy.getSee()) || StringUtils.isNotEmpty(pacsPatientStudy.getReportDiagnose());
                    String diagnosisStatus = hasReportData ? "2" : "-1";
                    pacsPatientStudy.setDiagnosisStatus(diagnosisStatus);

                    // 插入或更新记录
                    try {
                        if (existingStudy != null) {
                            pacsPatientStudy.setId(existingStudy.getId());
                            pacsPatientStudyMapper.updatePacsPatientStudy(pacsPatientStudy);
                            updateCount++;
                            log.debug("更新记录成功 - 患者: {}, 检查号: {}", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode());
                        } else {
                            pacsPatientStudyMapper.insertPacsPatientStudy(pacsPatientStudy);
                            insertCount++;
                            log.debug("插入记录成功 - 患者: {}, 检查号: {}", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode());
                        }
                    } catch (Exception e) {
                        errorCount++;
                        String error = String.format("保存记录失败 - 患者: %s, 检查号: %s, 错误: %s", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode(), e.getMessage());
                        errorDetails.add(error);
                        log.error(error, e);
                        continue;
                    }

                    // 处理院内诊断记录：如果有报告数据，则处理诊断记录
                    if (hasReportData) {
                        try {
                            processDiagnosisRecord(pacsPatientStudy);
                        } catch (Exception e) {
                            // 诊断记录处理失败不影响主流程
                            String error = String.format("处理诊断记录失败 - 患者: %s, 检查号: %s, 错误: %s", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode(), e.getMessage());
                            errorDetails.add(error);
                            log.warn(error, e);
                        }
                    }

                    // 每处理100条记录输出一次进度
                    if ((i + 1) % 100 == 0) {
                        log.info("同步进度: {}/{}, 新增: {}, 更新: {}, 错误: {}", i + 1, pacsPatientStudyList.size(), insertCount, updateCount, errorCount);
                    }
                } catch (Exception e) {
                    errorCount++;
                    String error = String.format("处理记录异常 - 患者: %s, 检查号: %s, 错误: %s", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode(), e.getMessage());
                    errorDetails.add(error);
                    log.error(error, e);
                }
            }

            // 4. 更新同步状态
            Date currentTime = new Date();
            long duration = currentTime.getTime() - startTime.getTime();
            String statusMessage = String.format("同步完成 - 总计: %d, 新增: %d, 更新: %d, 错误: %d, 耗时: %d秒", totalProcessed, insertCount, updateCount, errorCount, duration / 1000);

            if (errorCount > 0) {
                statusMessage += String.format(", 错误详情: %s", String.join("; ", errorDetails.subList(0, Math.min(3, errorDetails.size()))));
            }

            updateSyncStatus(SYNC_TYPE_STUDY, currentTime, errorCount == 0 ? "成功" : "部分成功", insertCount, updateCount, statusMessage);

            log.info("PACS检查数据同步完成: {}", statusMessage);

            // 如果有错误，记录详细错误信息
            if (!errorDetails.isEmpty()) {
                log.warn("同步过程中发现 {} 个错误:", errorDetails.size());
                for (String error : errorDetails) {
                    log.warn("  - {}", error);
                }
            }

            return insertCount + updateCount;

        } catch (Exception e) {
            long duration = new Date().getTime() - startTime.getTime();
            String errorMsg = String.format("PACS检查数据同步失败 - 已处理: %d, 新增: %d, 更新: %d, 耗时: %d秒, 错误: %s", totalProcessed, insertCount, updateCount, duration / 1000, e.getMessage());
            updateSyncStatus(SYNC_TYPE_STUDY, new Date(), "失败", insertCount, updateCount, errorMsg);
            log.error(errorMsg, e);
            throw e;
        }
    }

    /**
     * 根据系统参数确定DICOM同步标志
     * @return dicomSyncFlag值
     */
    private String determineDicomSyncFlag() {
        try {
            // 从系统参数表获取影像数据来源配置
            String imageDataSource = mainJdbcTemplate.queryForObject(
                    "SELECT config_value FROM sys_config WHERE config_key = 'image.data.source'",
                    String.class);

            if ("pacs".equals(imageDataSource)) {
                return "0"; // 需要从PACS同步DICOM数据
            } else if ("local".equals(imageDataSource)) {
                return "1"; // 本地已有数据，无需同步
            } else if ("mixed".equals(imageDataSource)) {
                return "2"; // 混合模式，按需同步
            }
        } catch (Exception e) {
            log.warn("获取影像数据来源配置失败，使用默认值: {}", e.getMessage());
        }

        // 默认需要同步
        return "0";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncPacsReportData() {
        try {
            Date lastSyncTime = getLastSyncTime(SYNC_TYPE_REPORT);

            List<PacsReport> reportList = getReportFromPacs(lastSyncTime);

            int insertCount = 0;
            int updateCount = 0;

            for (PacsReport report : reportList) {
                // 上传PDF文件到OSS
                String ossFileUrl = null;
                String localReportPath = report.getReportpath();
                if (localReportPath != null && localReportPath.toLowerCase().endsWith(".pdf")) {
                    try {
                        // 获取文件名
                        String fileName = localReportPath.substring(localReportPath.lastIndexOf(File.separator) + 1);
                        // 上传到OSS
                        ossFileUrl = ossFileUtils.uploadPdfToOss(localReportPath, fileName);
                        log.info("报告PDF已上传至OSS: {}", ossFileUrl);

                        // 更新报告路径为OSS URL
                        if (ossFileUrl != null) {
                            report.setReportpath(ossFileUrl);
                        }
                    } catch (Exception e) {
                        log.error("PDF上传OSS失败: {}", e.getMessage());
                        // 继续处理，即使上传失败也不中断同步流程
                    }
                }

                // 检查MySQL中是否已存在该记录
                PacsReport existingReport = pacsReportMapper.selectPacsReportByOriginalReportId(report.getHospitalId(), report.getOriginalReportId());

                if (existingReport != null) {
                    // 更新已存在的记录
                    pacsReportMapper.updatePacsReport(report);
                    updateCount++;
                } else {
                    // 插入新记录
                    pacsReportMapper.insertPacsReport(report);
                    insertCount++;
                }

                // 处理对应的pacs_patient_study记录，更新诊断状态并处理院内诊断记录
                try {
                    processReportForPatientStudy(report);
                } catch (Exception e) {
                    // 处理失败不影响报告同步主流程
                    log.warn("处理报告对应的患者检查记录失败 - 检查号: {}, 错误: {}", report.getExamCode(), e.getMessage(), e);
                }
            }

            // 更新同步状态
            Date currentTime = new Date();
            String statusMessage = String.format("成功同步报告数据，新增: %d, 更新: %d", insertCount, updateCount);
            updateSyncStatus(SYNC_TYPE_REPORT, currentTime, "成功", insertCount, updateCount, statusMessage);

            log.info("PACS报告数据同步完成: {}", statusMessage);

            return insertCount + updateCount;
        } catch (Exception e) {
            String errorMsg = "PACS报告数据同步失败：" + e.getMessage();
            updateSyncStatus(SYNC_TYPE_REPORT, new Date(), "失败", 0, 0, errorMsg);
            log.error(errorMsg, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncAllPacsData() {
        try {
            syncPacsPatientStudy();
            syncPacsReportData();
            return true;
        } catch (Exception e) {
            log.error("PACS全量数据同步失败", e);
            return false;
        }
    }

    @Override
    public String getSyncStatus() {
        StringBuilder status = new StringBuilder();

        PacsSyncStatus cloudStatus = pacsSyncStatusMapper.selectByType(SYNC_TYPE_STUDY);
        PacsSyncStatus reportStatus = pacsSyncStatusMapper.selectByType(SYNC_TYPE_REPORT);

        if (cloudStatus != null) {
            status.append("检查数据：").append(cloudStatus.getSyncStatus()).append(" - 新增: ").append(cloudStatus.getInsertCount()).append(", 更新: ").append(cloudStatus.getUpdateCount()).append("\n");

            if (cloudStatus.getLastSyncTime() != null) {
                status.append("检查数据最后同步时间：").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(cloudStatus.getLastSyncTime())).append("\n");
            }
        } else {
            status.append("检查数据：未同步\n");
        }

        if (reportStatus != null) {
            status.append("报告数据：").append(reportStatus.getSyncStatus()).append(" - 新增: ").append(reportStatus.getInsertCount()).append(", 更新: ").append(reportStatus.getUpdateCount()).append("\n");

            if (reportStatus.getLastSyncTime() != null) {
                status.append("报告数据最后同步时间：").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(reportStatus.getLastSyncTime())).append("\n");
            }
        } else {
            status.append("报告数据：未同步\n");
        }

        return status.toString();
    }

    /**
     * 验证患者检查数据的完整性
     */
    private boolean validatePatientStudyData(PacsPatientStudy study) {
        if (study == null) {
            log.warn("患者检查数据为空");
            return false;
        }

        if (StringUtils.isEmpty(study.getHospitalId())) {
            log.warn("医院ID为空 - 患者: {}", study.getPatientName());
            return false;
        }

        if (StringUtils.isEmpty(study.getExamCode())) {
            log.warn("检查号为空 - 患者: {}", study.getPatientName());
            return false;
        }

        if (StringUtils.isEmpty(study.getOriginalPatientId())) {
            log.warn("原始患者ID为空 - 检查号: {}", study.getExamCode());
            return false;
        }

        return true;
    }

    /**
     * 处理诊断记录
     */
    private void processDiagnosisRecord(PacsPatientStudy pacsPatientStudy) {
        try {
            // 检查是否有报告数据（影像所见或影像意见）
            boolean hasReportData = (StringUtils.isNotEmpty(pacsPatientStudy.getSee()) || StringUtils.isNotEmpty(pacsPatientStudy.getReportDiagnose()));

            if (!hasReportData) {
                log.debug("患者 {} 检查号 {} 暂无报告数据，跳过诊断记录处理", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode());
                return;
            }

            // 判断是否已经生成过诊断记录
            Diagnosis existingDiagnosis = diagnosisMapper.getByCheckId(pacsPatientStudy.getId());
            if (existingDiagnosis != null) {
                // 更新现有诊断记录
                existingDiagnosis.setDiagnose(pacsPatientStudy.getSee());
                existingDiagnosis.setRecommendation(pacsPatientStudy.getReportDiagnose());
                existingDiagnosis.setUpdateTime(new Date());
                existingDiagnosis.setUpdateBy("system");
                existingDiagnosis.setStatus("2");
                diagnosisMapper.updateDiagnosis(existingDiagnosis);

                log.debug("更新诊断记录成功 - 患者: {}, 检查号: {}", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode());
            } else {
                // 创建新的诊断记录
                Diagnosis diagnosis = new Diagnosis();
                diagnosis.setCheckId(pacsPatientStudy.getId());
                diagnosis.setPatientId(pacsPatientStudy.getOriginalPatientId());
                diagnosis.setDiagnose(pacsPatientStudy.getSee());
                diagnosis.setRecommendation(pacsPatientStudy.getReportDiagnose());
                diagnosis.setDoctor("system");
                diagnosis.setStatus("2");
                diagnosis.setDiagnosisType("1");// 院内诊断状态
                diagnosis.setAuditBy("system");
                diagnosis.setAuditTime(new Date());
                diagnosis.setCreateBy("system");
                diagnosis.setCreateTime(new Date());
                diagnosisMapper.insertDiagnosis(diagnosis);

                log.debug("创建诊断记录成功 - 患者: {}, 检查号: {}", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode());
            }
        } catch (Exception e) {
            log.error("处理诊断记录失败 - 患者: {}, 检查号: {}, 错误: {}", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新患者检查记录的诊断状态
     *
     * @param patientId       患者ID
     * @param diagnosisStatus 诊断状态
     */
    private void updatePatientStudyDiagnosisStatus(String patientId, String diagnosisStatus) {
        try {
            // 更新诊断状态
            pacsPatientStudyMapper.updateDiagnosisStatus(patientId, diagnosisStatus);

            log.debug("更新患者 {} 的诊断状态为 {}", patientId, diagnosisStatus);
        } catch (Exception e) {
            log.error("更新患者诊断状态失败 - 患者ID: {}, 诊断状态: {}, 错误: {}", patientId, diagnosisStatus, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理报告对应的患者检查记录
     * 当报告数据同步时，检查对应的pacs_patient_study记录，如果诊断状态为-1且有报告数据，则更新为院内诊断
     *
     * @param report 报告数据
     */
    private void processReportForPatientStudy(PacsReport report) {
        try {
            // 检查报告是否有诊断数据
            boolean hasReportData = StringUtils.isNotEmpty(report.getSee()) || StringUtils.isNotEmpty(report.getDiagnosis());

            if (!hasReportData) {
                log.debug("报告 {} 暂无诊断数据，跳过处理", report.getExamCode());
                return;
            }

            // 根据检查号查找对应的pacs_patient_study记录
            PacsPatientStudy pacsPatientStudy = pacsPatientStudyMapper.selectPacsPatientStudyByExamCode(report.getExamCode());
            if (pacsPatientStudy == null) {
                log.debug("未找到检查号 {} 对应的患者检查记录", report.getExamCode());
                return;
            }

            // 检查当前诊断状态是否为待诊断(-1)
            if (!"-1".equals(pacsPatientStudy.getDiagnosisStatus())) {
                log.debug("患者检查记录 {} 诊断状态为 {}，无需处理", report.getExamCode(), pacsPatientStudy.getDiagnosisStatus());
                return;
            }

            // 更新患者检查记录的报告数据
            pacsPatientStudy.setSee(report.getSee());
            pacsPatientStudy.setReportDiagnose(report.getDiagnosis());
            pacsPatientStudy.setDiagnosisStatus("2"); // 设置为院内诊断
            pacsPatientStudyMapper.updatePacsPatientStudy(pacsPatientStudy);

            // 处理诊断记录 - 使用报告中的医生和时间信息
            processDiagnosisRecordFromReport(pacsPatientStudy, report);

            log.info("成功处理报告对应的患者检查记录 - 检查号: {}, 患者: {}", report.getExamCode(), pacsPatientStudy.getPatientName());

        } catch (Exception e) {
            log.error("处理报告对应的患者检查记录失败 - 检查号: {}, 错误: {}", report.getExamCode(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从报告数据处理诊断记录
     * 使用报告中的医生信息和时间信息创建诊断记录
     *
     * @param pacsPatientStudy 患者检查记录
     * @param report           报告数据
     */
    private void processDiagnosisRecordFromReport(PacsPatientStudy pacsPatientStudy, PacsReport report) {
        try {
            // 判断是否已经生成过诊断记录
            Diagnosis existingDiagnosis = diagnosisMapper.getByCheckId(pacsPatientStudy.getId());
            if (existingDiagnosis != null) {
                // 更新现有诊断记录 - 使用报告中的信息
                existingDiagnosis.setDiagnose(report.getSee());
                existingDiagnosis.setRecommendation(report.getDiagnosis());
                existingDiagnosis.setDoctor(StringUtils.isNotEmpty(report.getDoctorName()) ? report.getDoctorName() : "system");
                existingDiagnosis.setUpdateTime(report.getLastAuditTime() != null ? report.getLastAuditTime() : new Date());
                existingDiagnosis.setUpdateBy(StringUtils.isNotEmpty(report.getDoctorName()) ? report.getDoctorName() : "system");
                existingDiagnosis.setAuditTime(report.getLastAuditTime() != null ? report.getLastAuditTime() : new Date());
                existingDiagnosis.setAuditBy(StringUtils.isNotEmpty(report.getDoctorName()) ? report.getDoctorName() : "system");

                // 如果报告有审核信息，更新审核相关字段
                if (report.getLastAuditTime() != null) {
                    existingDiagnosis.setAuditBy(StringUtils.isNotEmpty(report.getReviewDoctorName()) ? report.getReviewDoctorName() : report.getDoctorName());
                    existingDiagnosis.setAuditTime(report.getLastAuditTime());
                }

                diagnosisMapper.updateDiagnosis(existingDiagnosis);
                log.debug("更新诊断记录成功（来自报告） - 患者: {}, 检查号: {}, 医生: {}", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode(), report.getDoctorName());
            } else {
                // 创建新的诊断记录 - 使用报告中的信息
                Diagnosis diagnosis = new Diagnosis();
                diagnosis.setCheckId(pacsPatientStudy.getId());
                diagnosis.setPatientId(pacsPatientStudy.getOriginalPatientId());
                diagnosis.setDiagnose(report.getSee());
                diagnosis.setRecommendation(report.getDiagnosis());
                diagnosis.setDoctor(StringUtils.isNotEmpty(report.getDoctorName()) ? report.getDoctorName() : "system");
                diagnosis.setStatus("2"); //
                diagnosis.setDiagnosisType("1");//1：院内诊断 2：云端诊断

                // 设置审核信息
                diagnosis.setAuditBy(StringUtils.isNotEmpty(report.getReviewDoctorName()) ? report.getReviewDoctorName() : report.getDoctorName());
                diagnosis.setAuditTime(report.getLastAuditTime() != null ? report.getLastAuditTime() : new Date());

                // 设置创建信息
                diagnosis.setCreateBy(StringUtils.isNotEmpty(report.getDoctorName()) ? report.getDoctorName() : "system");
                diagnosis.setCreateTime(report.getWriteTime() != null ? report.getWriteTime() : new Date());

                diagnosisMapper.insertDiagnosis(diagnosis);
                log.debug("创建诊断记录成功（来自报告） - 患者: {}, 检查号: {}, 医生: {}", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode(), report.getDoctorName());
            }

            // 更新pacs_patient_study表的诊断状态为院内诊断
            updatePatientStudyDiagnosisStatus(pacsPatientStudy.getOriginalPatientId(), "2");

        } catch (Exception e) {
            log.error("从报告处理诊断记录失败 - 患者: {}, 检查号: {}, 错误: {}", pacsPatientStudy.getPatientName(), pacsPatientStudy.getExamCode(), e.getMessage(), e);
            throw e;
        }
    }

    public List<PacsPatientStudy> getPatientStudyFromPacs(Date lastSyncTime) {
        log.info("从PACS数据源查询患者检查数据，上次同步时间: {}", lastSyncTime);

        // 检测数据库类型并使用相应的日期格式
        try {
            // 首先尝试使用参数化查询（推荐方式）
            String sql = "SELECT SEE, REPORTDIAGNOSE as report_diagnose, HOSPITALID AS hospital_id, HOSPITALNAME AS hospital_name, ORIGINALPATIENTID AS original_patient_id, PATIENTNAME AS patient_name, ORIGINALEXAMCODE AS original_exam_code, EXAMCODE AS exam_code, MODALITY AS modality, MOBILE AS mobile, PATIENTSEX AS patient_sex, HISPATIENTID AS his_patient_id, OUTPATIENTID AS out_patient_id, INPATIENTID AS in_patient_id, EXAMPATIENTID AS exam_patient_id, PATIENTFROM AS patient_from, BEDNO AS bed_no, PATIENTBIRTHDAY AS patient_birthday, ORGAN AS organ, EXAMITEM AS exam_item, EXAMDEPARTMENT AS exam_department, EXAMDOCTORNAME AS exam_doctor_name, REGISTERTIME AS register_time, RESERVETIME AS reserve_time, RESERVEARRIVALTIME AS reserve_arrival_time, CHECKFINISHTIME AS check_finish_time, IDNO AS id_no, SOCIALSECURITYCARDNO AS social_security_card_no, MEDICALCARDNO AS medical_card_no, MEDICALHISTORY AS medical_history, CLINICALSYMPTOM AS clinical_symptom, CLINICALDIAGNOSIS AS clinical_diagnosis, DEVICE AS device FROM V_PACS_PATIENT_STUDY WHERE CHECKFINISHTIME > ?";

            return pacsJdbcTemplate.query(sql, new Object[]{lastSyncTime}, (rs, rowNum) -> {
                PacsPatientStudy study = new PacsPatientStudy();
                study.setHospitalId(rs.getString("hospital_id"));
                study.setHospitalName(rs.getString("hospital_name"));
                study.setOriginalPatientId(rs.getString("original_patient_id"));
                study.setPatientName(rs.getString("patient_name"));
                study.setOriginalExamCode(rs.getString("original_exam_code"));
                study.setExamCode(rs.getString("exam_code"));
                study.setModality(rs.getString("modality"));
                study.setMobile(rs.getString("mobile"));
                study.setPatientSex(rs.getString("patient_sex"));
                study.setHisPatientId(rs.getString("his_patient_id"));
                study.setOutPatientId(rs.getString("out_patient_id"));
                study.setInPatientId(rs.getString("in_patient_id"));
                study.setExamPatientId(rs.getString("exam_patient_id"));
                study.setPatientFrom(rs.getString("patient_from"));
                study.setBedNo(rs.getString("bed_no"));
                study.setPatientBirthday(rs.getDate("patient_birthday"));
                study.setOrgan(rs.getString("organ"));
                study.setExamItem(rs.getString("exam_item"));
                study.setExamDepartment(rs.getString("exam_department"));
                study.setExamDoctorName(rs.getString("exam_doctor_name"));
                study.setRegisterTime(rs.getTimestamp("register_time"));
                study.setReserveTime(rs.getTimestamp("reserve_time"));
                study.setReserveArrivalTime(rs.getTimestamp("reserve_arrival_time"));
                study.setCheckFinishTime(rs.getTimestamp("check_finish_time"));
                study.setIdNo(rs.getString("id_no"));
                study.setSocialSecurityCardNo(rs.getString("social_security_card_no"));
                study.setMedicalCardNo(rs.getString("medical_card_no"));
                study.setMedicalHistory(rs.getString("medical_history"));
                study.setClinicalSymptom(rs.getString("clinical_symptom"));
                study.setClinicalDiagnosis(rs.getString("clinical_diagnosis"));
                study.setDevice(rs.getString("device"));
                study.setReportDiagnose(rs.getString("report_diagnose"));
                study.setSee(rs.getString("SEE"));
                return study;
            });

        } catch (Exception parameterException) {
            log.warn("参数化查询失败，尝试使用数据库特定的日期格式: {}", parameterException.getMessage());

            // 如果参数化查询失败，尝试检测数据库类型并使用相应的日期格式
            try {
                String databaseProductName = pacsJdbcTemplate.getDataSource().getConnection().getMetaData().getDatabaseProductName().toLowerCase();
                log.info("检测到数据库类型: {}", databaseProductName);

                String sqlWithFormattedDate;
                if (databaseProductName.contains("oracle")) {
                    // Oracle数据库使用TO_DATE函数
                    SimpleDateFormat oracleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String formattedTime = oracleFormat.format(lastSyncTime);
                    sqlWithFormattedDate = "SELECT SEE, REPORTDIAGNOSE as report_diagnose, HOSPITALID AS hospital_id, HOSPITALNAME AS hospital_name, ORIGINALPATIENTID AS original_patient_id, PATIENTNAME AS patient_name, ORIGINALEXAMCODE AS original_exam_code, EXAMCODE AS exam_code, MODALITY AS modality, MOBILE AS mobile, PATIENTSEX AS patient_sex, HISPATIENTID AS his_patient_id, OUTPATIENTID AS out_patient_id, INPATIENTID AS in_patient_id, EXAMPATIENTID AS exam_patient_id, PATIENTFROM AS patient_from, BEDNO AS bed_no, PATIENTBIRTHDAY AS patient_birthday, ORGAN AS organ, EXAMITEM AS exam_item, EXAMDEPARTMENT AS exam_department, EXAMDOCTORNAME AS exam_doctor_name, REGISTERTIME AS register_time, RESERVETIME AS reserve_time, RESERVEARRIVALTIME AS reserve_arrival_time, CHECKFINISHTIME AS check_finish_time, IDNO AS id_no, SOCIALSECURITYCARDNO AS social_security_card_no, MEDICALCARDNO AS medical_card_no, MEDICALHISTORY AS medical_history, CLINICALSYMPTOM AS clinical_symptom, CLINICALDIAGNOSIS AS clinical_diagnosis, DEVICE AS device FROM V_PACS_PATIENT_STUDY WHERE CHECKFINISHTIME > TO_DATE('" + formattedTime + "', 'YYYY-MM-DD HH24:MI:SS')";
                    log.info("使用Oracle日期格式: TO_DATE('{}', 'YYYY-MM-DD HH24:MI:SS')", formattedTime);
                } else {
                    // SQL Server或其他数据库使用标准格式
                    SimpleDateFormat sqlServerFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String formattedTime = sqlServerFormat.format(lastSyncTime);
                    sqlWithFormattedDate = "SELECT SEE, REPORTDIAGNOSE as report_diagnose, HOSPITALID AS hospital_id, HOSPITALNAME AS hospital_name, ORIGINALPATIENTID AS original_patient_id, PATIENTNAME AS patient_name, ORIGINALEXAMCODE AS original_exam_code, EXAMCODE AS exam_code, MODALITY AS modality, MOBILE AS mobile, PATIENTSEX AS patient_sex, HISPATIENTID AS his_patient_id, OUTPATIENTID AS out_patient_id, INPATIENTID AS in_patient_id, EXAMPATIENTID AS exam_patient_id, PATIENTFROM AS patient_from, BEDNO AS bed_no, PATIENTBIRTHDAY AS patient_birthday, ORGAN AS organ, EXAMITEM AS exam_item, EXAMDEPARTMENT AS exam_department, EXAMDOCTORNAME AS exam_doctor_name, REGISTERTIME AS register_time, RESERVETIME AS reserve_time, RESERVEARRIVALTIME AS reserve_arrival_time, CHECKFINISHTIME AS check_finish_time, IDNO AS id_no, SOCIALSECURITYCARDNO AS social_security_card_no, MEDICALCARDNO AS medical_card_no, MEDICALHISTORY AS medical_history, CLINICALSYMPTOM AS clinical_symptom, CLINICALDIAGNOSIS AS clinical_diagnosis, DEVICE AS device FROM V_PACS_PATIENT_STUDY WHERE CHECKFINISHTIME > '" + formattedTime + "'";
                    log.info("使用SQL Server日期格式: '{}'", formattedTime);
                }

                return pacsJdbcTemplate.query(sqlWithFormattedDate, (rs, rowNum) -> {
                    PacsPatientStudy study = new PacsPatientStudy();
                    study.setHospitalId(rs.getString("hospital_id"));
                    study.setHospitalName(rs.getString("hospital_name"));
                    study.setOriginalPatientId(rs.getString("original_patient_id"));
                    study.setPatientName(rs.getString("patient_name"));
                    study.setOriginalExamCode(rs.getString("original_exam_code"));
                    study.setExamCode(rs.getString("exam_code"));
                    study.setModality(rs.getString("modality"));
                    study.setMobile(rs.getString("mobile"));
                    study.setPatientSex(rs.getString("patient_sex"));
                    study.setHisPatientId(rs.getString("his_patient_id"));
                    study.setOutPatientId(rs.getString("out_patient_id"));
                    study.setInPatientId(rs.getString("in_patient_id"));
                    study.setExamPatientId(rs.getString("exam_patient_id"));
                    study.setPatientFrom(rs.getString("patient_from"));
                    study.setBedNo(rs.getString("bed_no"));
                    study.setPatientBirthday(rs.getDate("patient_birthday"));
                    study.setOrgan(rs.getString("organ"));
                    study.setExamItem(rs.getString("exam_item"));
                    study.setExamDepartment(rs.getString("exam_department"));
                    study.setExamDoctorName(rs.getString("exam_doctor_name"));
                    study.setRegisterTime(rs.getTimestamp("register_time"));
                    study.setReserveTime(rs.getTimestamp("reserve_time"));
                    study.setReserveArrivalTime(rs.getTimestamp("reserve_arrival_time"));
                    study.setCheckFinishTime(rs.getTimestamp("check_finish_time"));
                    study.setIdNo(rs.getString("id_no"));
                    study.setSocialSecurityCardNo(rs.getString("social_security_card_no"));
                    study.setMedicalCardNo(rs.getString("medical_card_no"));
                    study.setMedicalHistory(rs.getString("medical_history"));
                    study.setClinicalSymptom(rs.getString("clinical_symptom"));
                    study.setClinicalDiagnosis(rs.getString("clinical_diagnosis"));
                    study.setDevice(rs.getString("device"));
                    study.setReportDiagnose(rs.getString("report_diagnose"));
                    study.setSee(rs.getString("SEE"));
                    return study;
                });

            } catch (Exception fallbackException) {
                log.error("数据库特定格式查询也失败: {}", fallbackException.getMessage(), fallbackException);
                throw new RuntimeException("无法查询PACS患者检查数据，日期转换失败", fallbackException);
            }
        }
    }

    public List<PacsReport> getReportFromPacs(Date lastSyncTime) {
        log.info("从PACS数据源查询报告数据，上次同步时间: {}", lastSyncTime);

        // 检测数据库类型并使用相应的日期格式
        try {
            // 首先尝试使用参数化查询（推荐方式）
            String sql = "SELECT HOSPITALID AS hospital_id, HOSPITALNAME AS hospital_name, ORIGINALPATIENTID AS original_patient_id, ORIGINALEXAMCODE AS original_exam_code, EXAMCODE AS exam_code, ORIGINALREPORTID AS original_report_id, MODALITY AS modality, SEE AS see, DIAGNOSIS AS diagnosis, REPORTDEPARTMENT AS report_department, DOCTORID AS doctor_id, DOCTORNAME AS doctor_name, REVIEWDOCTORID AS review_doctor_id, REVIEWDOCTORNAME AS review_doctor_name, WRITETIME AS write_time, AUDITTIME AS audit_time, LASTAUDITTIME AS last_audit_time, REPORTSTATE AS report_state, POSITIVE AS positive, INFECTIOUSDISEASE AS infectious_disease, REPORTPATH AS reportpath FROM V_PACS_REPORT WHERE LASTAUDITTIME > ?";

            return pacsJdbcTemplate.query(sql, new Object[]{lastSyncTime}, (rs, rowNum) -> {
                PacsReport report = new PacsReport();
                report.setHospitalId(rs.getString("hospital_id"));
                report.setHospitalName(rs.getString("hospital_name"));
                report.setOriginalPatientId(rs.getString("original_patient_id"));
                report.setOriginalExamCode(rs.getString("original_exam_code"));
                report.setExamCode(rs.getString("exam_code"));
                report.setOriginalReportId(rs.getString("original_report_id"));
                report.setModality(rs.getString("modality"));
                report.setSee(rs.getString("see"));
                report.setDiagnosis(rs.getString("diagnosis"));
                report.setReportDepartment(rs.getString("report_department"));
                report.setDoctorId(rs.getString("doctor_id"));
                report.setDoctorName(rs.getString("doctor_name"));
                report.setReviewDoctorId(rs.getString("review_doctor_id"));
                report.setReviewDoctorName(rs.getString("review_doctor_name"));
                report.setWriteTime(rs.getTimestamp("write_time"));
                report.setAuditTime(rs.getTimestamp("audit_time"));
                report.setLastAuditTime(rs.getTimestamp("last_audit_time"));
                report.setReportState(rs.getInt("report_state"));
                report.setPositive(rs.getString("positive"));
                report.setInfectiousDisease(rs.getString("infectious_disease"));
                report.setReportpath(rs.getString("reportpath"));
                return report;
            });

        } catch (Exception parameterException) {
            log.warn("参数化查询失败，尝试使用数据库特定的日期格式: {}", parameterException.getMessage());

            // 如果参数化查询失败，尝试检测数据库类型并使用相应的日期格式
            try {
                String databaseProductName = pacsJdbcTemplate.getDataSource().getConnection().getMetaData().getDatabaseProductName().toLowerCase();
                log.info("检测到数据库类型: {}", databaseProductName);

                String sqlWithFormattedDate;
                if (databaseProductName.contains("oracle")) {
                    // Oracle数据库使用TO_DATE函数
                    SimpleDateFormat oracleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String formattedTime = oracleFormat.format(lastSyncTime);
                    sqlWithFormattedDate = "SELECT HOSPITALID AS hospital_id, HOSPITALNAME AS hospital_name, ORIGINALPATIENTID AS original_patient_id, ORIGINALEXAMCODE AS original_exam_code, EXAMCODE AS exam_code, ORIGINALREPORTID AS original_report_id, MODALITY AS modality, SEE AS see, DIAGNOSIS AS diagnosis, REPORTDEPARTMENT AS report_department, DOCTORID AS doctor_id, DOCTORNAME AS doctor_name, REVIEWDOCTORID AS review_doctor_id, REVIEWDOCTORNAME AS review_doctor_name, WRITETIME AS write_time, AUDITTIME AS audit_time, LASTAUDITTIME AS last_audit_time, REPORTSTATE AS report_state, POSITIVE AS positive, INFECTIOUSDISEASE AS infectious_disease, REPORTPATH AS reportpath FROM V_PACS_REPORT WHERE LASTAUDITTIME > TO_DATE('" + formattedTime + "', 'YYYY-MM-DD HH24:MI:SS')";
                    log.info("使用Oracle日期格式: TO_DATE('{}', 'YYYY-MM-DD HH24:MI:SS')", formattedTime);
                } else {
                    // SQL Server或其他数据库使用标准格式
                    SimpleDateFormat sqlServerFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String formattedTime = sqlServerFormat.format(lastSyncTime);
                    sqlWithFormattedDate = "SELECT HOSPITALID AS hospital_id, HOSPITALNAME AS hospital_name, ORIGINALPATIENTID AS original_patient_id, ORIGINALEXAMCODE AS original_exam_code, EXAMCODE AS exam_code, ORIGINALREPORTID AS original_report_id, MODALITY AS modality, SEE AS see, DIAGNOSIS AS diagnosis, REPORTDEPARTMENT AS report_department, DOCTORID AS doctor_id, DOCTORNAME AS doctor_name, REVIEWDOCTORID AS review_doctor_id, REVIEWDOCTORNAME AS review_doctor_name, WRITETIME AS write_time, AUDITTIME AS audit_time, LASTAUDITTIME AS last_audit_time, REPORTSTATE AS report_state, POSITIVE AS positive, INFECTIOUSDISEASE AS infectious_disease, REPORTPATH AS reportpath FROM V_PACS_REPORT WHERE LASTAUDITTIME > '" + formattedTime + "'";
                    log.info("使用SQL Server日期格式: '{}'", formattedTime);
                }

                return pacsJdbcTemplate.query(sqlWithFormattedDate, (rs, rowNum) -> {
                    PacsReport report = new PacsReport();
                    report.setHospitalId(rs.getString("hospital_id"));
                    report.setHospitalName(rs.getString("hospital_name"));
                    report.setOriginalPatientId(rs.getString("original_patient_id"));
                    report.setOriginalExamCode(rs.getString("original_exam_code"));
                    report.setExamCode(rs.getString("exam_code"));
                    report.setOriginalReportId(rs.getString("original_report_id"));
                    report.setModality(rs.getString("modality"));
                    report.setSee(rs.getString("see"));
                    report.setDiagnosis(rs.getString("diagnosis"));
                    report.setReportDepartment(rs.getString("report_department"));
                    report.setDoctorId(rs.getString("doctor_id"));
                    report.setDoctorName(rs.getString("doctor_name"));
                    report.setReviewDoctorId(rs.getString("review_doctor_id"));
                    report.setReviewDoctorName(rs.getString("review_doctor_name"));
                    report.setWriteTime(rs.getTimestamp("write_time"));
                    report.setAuditTime(rs.getTimestamp("audit_time"));
                    report.setLastAuditTime(rs.getTimestamp("last_audit_time"));
                    report.setReportState(rs.getInt("report_state"));
                    report.setPositive(rs.getString("positive"));
                    report.setInfectiousDisease(rs.getString("infectious_disease"));
                    report.setReportpath(rs.getString("reportpath"));
                    return report;
                });

            } catch (Exception fallbackException) {
                log.error("数据库特定格式查询也失败: {}", fallbackException.getMessage(), fallbackException);
                throw new RuntimeException("无法查询PACS报告数据，日期转换失败", fallbackException);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int fixMissingDiagnosisRecords() {
        log.info("开始修正线上数据 - 为diagnosis_status为2但没有对应diagnosis记录的数据补充diagnosis记录");

        int fixedCount = 0;
        List<String> errorDetails = new ArrayList<>();

        try {
            // 查询diagnosis_status为2但没有对应diagnosis记录的pacs_patient_study记录
            String querySQL = "SELECT ps.id, ps.patient_name, ps.exam_code, ps.see, ps.report_diagnose, " +
                            "ps.exam_doctor_name, ps.check_finish_time " +
                            "FROM pacs_patient_study ps " +
                            "LEFT JOIN diagnosis d ON d.check_id = ps.id " +
                            "WHERE ps.diagnosis_status = '2' AND d.id IS NULL";

            List<PacsPatientStudy> studiesNeedingDiagnosis = mainJdbcTemplate.query(querySQL, (rs, rowNum) -> {
                PacsPatientStudy study = new PacsPatientStudy();
                study.setId(rs.getLong("id"));
                study.setPatientName(rs.getString("patient_name"));
                study.setExamCode(rs.getString("exam_code"));
                study.setSee(rs.getString("see"));
                study.setReportDiagnose(rs.getString("report_diagnose"));
                study.setExamDoctorName(rs.getString("exam_doctor_name"));
                study.setCheckFinishTime(rs.getTimestamp("check_finish_time"));
                return study;
            });

            log.info("找到 {} 条需要补充diagnosis记录的数据", studiesNeedingDiagnosis.size());

            for (PacsPatientStudy study : studiesNeedingDiagnosis) {
                try {
                    // 创建diagnosis记录
                    Diagnosis diagnosis = new Diagnosis();
                    diagnosis.setCheckId(study.getId());

                    // 构建诊断内容 - 优先使用report_diagnose，其次使用see
                    String diagnoseContent = StringUtils.isNotEmpty(study.getReportDiagnose()) ?
                        study.getReportDiagnose() : study.getSee();
                    diagnosis.setDiagnose(diagnoseContent);

                    // 设置诊断医生
                    diagnosis.setDoctor(StringUtils.isNotEmpty(study.getExamDoctorName()) ?
                        study.getExamDoctorName() : "系统补充");

                    // 设置诊断类型为院内诊断
                    diagnosis.setDiagnosisType("1");

                    // 设置状态为已诊断
                    diagnosis.setStatus("2");

                    // 设置创建信息
                    diagnosis.setCreateBy("system_fix");
                    diagnosis.setCreateTime(study.getCheckFinishTime() != null ?
                        study.getCheckFinishTime() : new Date());

                    // 插入diagnosis记录
                    diagnosisMapper.insertDiagnosis(diagnosis);

                    fixedCount++;
                    log.debug("成功补充diagnosis记录 - 患者: {}, 检查号: {}, ID: {}",
                        study.getPatientName(), study.getExamCode(), study.getId());

                } catch (Exception e) {
                    String error = String.format("补充diagnosis记录失败 - 患者: %s, 检查号: %s, ID: %d, 错误: %s",
                        study.getPatientName(), study.getExamCode(), study.getId(), e.getMessage());
                    errorDetails.add(error);
                    log.error(error, e);
                }
            }

            log.info("修正线上数据完成 - 成功补充 {} 条diagnosis记录", fixedCount);

            if (!errorDetails.isEmpty()) {
                log.warn("修正过程中发现 {} 个错误:", errorDetails.size());
                for (String error : errorDetails) {
                    log.warn("  - {}", error);
                }
            }

            return fixedCount;

        } catch (Exception e) {
            log.error("修正线上数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
