package com.ruoyi.datasync.task;

import org.springframework.stereotype.Component;
import com.ruoyi.datasync.service.PacsDataSyncService;
import lombok.extern.slf4j.Slf4j;

/**
 * PACS数据同步定时任务
 */
@Slf4j
@Component("pacsSyncJob")
public class PacsSyncJob {
    
    private final PacsDataSyncService syncService;
    
    public PacsSyncJob(PacsDataSyncService syncService) {
        this.syncService = syncService;
    }
    
    /**
     * 同步检查数据
     */
    public void syncPacsPatientStudy() {
        log.info("开始执行PACS检查数据同步任务");
        try {
            syncService.syncPacsPatientStudy();
            log.info("PACS检查数据同步任务执行完成");
        } catch (Exception e) {
            log.error("PACS检查数据同步任务执行失败", e);
            throw e;
        }
    }

    /**
     * 同步报告数据
     */
    public void syncPacsReportData() {
        log.info("开始执行PACS报告数据同步任务");
        try {
            syncService.syncPacsReportData();
            log.info("PACS报告数据同步任务执行完成");
        } catch (Exception e) {
            log.error("PACS报告数据同步任务执行失败", e);
            throw e;
        }
    }
    
    /**
     * 同步所有PACS数据
     */
    public void syncAllPacsData() {
        log.info("开始执行PACS全量数据同步任务");
        try {
            syncService.syncAllPacsData();
            log.info("PACS全量数据同步任务执行完成");
        } catch (Exception e) {
            log.error("PACS全量数据同步任务执行失败", e);
            throw e;
        }
    }

    /**
     * 修正线上数据 - 补充缺失的diagnosis记录
     */
    public void fixMissingDiagnosisRecords() {
        log.info("开始执行线上数据修正任务 - 补充缺失的diagnosis记录");
        try {
            int fixedCount = syncService.fixMissingDiagnosisRecords();
            log.info("线上数据修正任务执行完成，共修正 {} 条记录", fixedCount);
        } catch (Exception e) {
            log.error("线上数据修正任务执行失败", e);
            throw e;
        }
    }
}
