package com.ruoyi.diagnosis.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.diagnosis.domain.DiagnosisDict;
import com.ruoyi.diagnosis.service.IDiagnosisDictService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 诊断字典Controller
 * 
 * <AUTHOR>
 * @date 2025-05-11
 */
@RestController
@RequestMapping("/diagnosis/dict")
public class DiagnosisDictController extends BaseController
{
    @Autowired
    private IDiagnosisDictService diagnosisDictService;

    /**
     * 查询诊断字典列表
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:dict:list')")
    @GetMapping("/list")
    public TableDataInfo list(DiagnosisDict diagnosisDict)
    {
        startPage();
        List<DiagnosisDict> list = diagnosisDictService.selectDiagnosisDictList(diagnosisDict);
        return getDataTable(list);
    }

    /**
     * 导出诊断字典列表
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:dict:export')")
    @Log(title = "诊断字典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DiagnosisDict diagnosisDict)
    {
        List<DiagnosisDict> list = diagnosisDictService.selectDiagnosisDictList(diagnosisDict);
        ExcelUtil<DiagnosisDict> util = new ExcelUtil<DiagnosisDict>(DiagnosisDict.class);
        util.exportExcel(response, list, "诊断字典数据");
    }

    /**
     * 获取诊断字典详细信息
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:dict:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(diagnosisDictService.selectDiagnosisDictById(id));
    }

    /**
     * 新增诊断字典
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:dict:add')")
    @Log(title = "诊断字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DiagnosisDict diagnosisDict)
    {
        return toAjax(diagnosisDictService.insertDiagnosisDict(diagnosisDict));
    }

    /**
     * 修改诊断字典
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:dict:edit')")
    @Log(title = "诊断字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DiagnosisDict diagnosisDict)
    {
        return toAjax(diagnosisDictService.updateDiagnosisDict(diagnosisDict));
    }

    /**
     * 删除诊断字典
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:dict:remove')")
    @Log(title = "诊断字典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(diagnosisDictService.deleteDiagnosisDictByIds(ids));
    }
}
