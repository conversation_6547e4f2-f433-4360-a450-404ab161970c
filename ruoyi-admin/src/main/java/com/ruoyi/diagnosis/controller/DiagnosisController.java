package com.ruoyi.diagnosis.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.diagnosis.domain.Diagnosis;
import com.ruoyi.diagnosis.service.IDiagnosisService;
import com.ruoyi.diagnosis.service.DiagnosisPdfGeneratorService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import java.util.HashMap;
import java.util.Map;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysConfigService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;
import java.sql.Timestamp;

/**
 * 诊断Controller
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@RestController
@RequestMapping("/diagnosis/diagnosis")
public class DiagnosisController extends BaseController
{
    @Autowired
    private IDiagnosisService diagnosisService;

    @Autowired
    private DiagnosisPdfGeneratorService pdfGeneratorService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 查询诊断列表
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:list')")
    @GetMapping("/list")
    public TableDataInfo list(Diagnosis diagnosis)
    {
        startPage();
        List<Diagnosis> list = diagnosisService.selectDiagnosisList(diagnosis);
        return getDataTable(list);
    }

    /**
     * 导出诊断列表
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:export')")
    @Log(title = "诊断", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Diagnosis diagnosis)
    {
        List<Diagnosis> list = diagnosisService.selectDiagnosisList(diagnosis);
        ExcelUtil<Diagnosis> util = new ExcelUtil<Diagnosis>(Diagnosis.class);
        util.exportExcel(response, list, "诊断数据");
    }

    /**
     * 获取诊断详细信息
     */
    //@PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(diagnosisService.selectDiagnosisById(id));
    }

    /**
     * 新增诊断
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:add')")
    @Log(title = "诊断", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Diagnosis diagnosis)
    {
        int result = diagnosisService.insertDiagnosis(diagnosis);
        if (result > 0) {
            // 返回保存后的诊断数据
            return success(diagnosis);
        } else {
            return error("新增诊断失败");
        }
    }

    /**
     * 修改诊断
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:edit')")
    @Log(title = "诊断", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Diagnosis diagnosis)
    {
        int result = diagnosisService.updateDiagnosis(diagnosis);
        if (result > 0) {
            // 返回更新后的诊断数据
            return success(diagnosis);
        } else {
            return error("修改诊断失败");
        }
    }

    /**
     * 删除诊断
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:remove')")
    @Log(title = "诊断", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(diagnosisService.deleteDiagnosisByIds(ids));
    }

    /**
     * 审核诊断
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:audit')")
    @Log(title = "审核诊断", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{id}")
    public AjaxResult audit(@PathVariable("id") Long id)
    {
        return toAjax(diagnosisService.auditDiagnosis(id));
    }

    /**
     * 反审核诊断
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:audit')")
    @Log(title = "反审核诊断", businessType = BusinessType.UPDATE)
    @PutMapping("/unaudit/{id}")
    public AjaxResult unaudit(@PathVariable("id") Long id)
    {
        return toAjax(diagnosisService.unauditDiagnosis(id));
    }

    /**
     * 驳回诊断
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:audit')")
    @Log(title = "驳回诊断", businessType = BusinessType.UPDATE)
    @PutMapping("/reject")
    public AjaxResult reject(@RequestBody Diagnosis diagnosis)
    {
        return toAjax(diagnosisService.rejectDiagnosis(diagnosis));
    }

    /**
     * 撤销审核
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:audit')")
    @Log(title = "撤销审核", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{id}")
    public AjaxResult cancel(@PathVariable("id") Long id)
    {
        return toAjax(diagnosisService.cancelAudit(id));
    }

    /**
     * 提交审核
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:edit')")
    @Log(title = "提交审核", businessType = BusinessType.UPDATE)
    @PutMapping("/submit/{id}")
    public AjaxResult submit(@PathVariable("id") Long id)
    {
        return toAjax(diagnosisService.submitForAudit(id));
    }

    /**
     * 格式化报告数据
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:diagnosis:query')")
    @GetMapping("/formatReportData/{id}")
    public AjaxResult formatReportData(@PathVariable("id") Long id)
    {
        try {
            // 获取诊断信息
            Diagnosis diagnosis = diagnosisService.selectDiagnosisById(id);
            if (diagnosis == null) {
                return error("诊断信息不存在");
            }

            // 调用PDF生成服务的报告数据准备方法
            Map<String, Object> reportData = pdfGeneratorService.prepareReportDataPublic(diagnosis);

            return success( reportData);
        } catch (Exception e) {
            logger.error("格式化报告数据失败，诊断ID: {}", id, e);
            return error("格式化报告数据失败: " + e.getMessage());
        }
    }

    /**
     * 测试当前用户签名图片
     */
    @GetMapping("/test-signature")
    public AjaxResult testCurrentUserSignature() {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getUserId();
            if (userId == null) {
                return error("未获取到当前登录用户信息");
            }

            // 查询用户信息
            SysUser user = sysUserService.selectUserById(userId);
            if (user == null) {
                return error("用户信息不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("userName", user.getUserName());
            result.put("nickName", user.getNickName());
            result.put("avatar", user.getAvatar());

            // 测试签名图片获取逻辑
            String signature = user.getAvatar();
            if (StringUtils.isNotEmpty(signature) && !signature.startsWith("http")) {
                // 模拟转换为完整URL的逻辑
                signature = "http://localhost:9000/" + signature;
            }
            result.put("signatureUrl", signature);
            result.put("hasSignature", StringUtils.isNotEmpty(user.getAvatar()));

            return success(result);
        } catch (Exception e) {
            logger.error("测试用户签名失败", e);
            return error("测试用户签名失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否有新的待诊断患者
     */
    @GetMapping("/check-new-patients")
    public AjaxResult checkNewPatients() {
        try {
            // 检查新患者提醒功能是否开启
            String notificationEnabled = sysConfigService.selectConfigByKey("diagnosis.notification.enabled");
            if (!"true".equals(notificationEnabled)) {
                return success(Map.of("hasNew", false, "enabled", false));
            }

            // 获取上次检查时间（从前端传递或使用默认值）
            String lastCheckTimeStr = sysConfigService.selectConfigByKey("diagnosis.notification.lastCheckTime");
            LocalDateTime lastCheckTime;
            if (StringUtils.isNotEmpty(lastCheckTimeStr)) {
                lastCheckTime = LocalDateTime.parse(lastCheckTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } else {
                // 默认检查最近5分钟的数据
                lastCheckTime = LocalDateTime.now().minusMinutes(5);
            }

            // 查询在指定时间之后创建的待诊断记录
            Diagnosis queryDiagnosis = new Diagnosis();
            queryDiagnosis.setStatus("-1"); // 待诊断状态
            List<Diagnosis> allPendingList = diagnosisService.selectDiagnosisList(queryDiagnosis);

            // 筛选出新增的记录（这里简化处理，实际可以在SQL中加时间条件）
            long newCount = allPendingList.stream()
                .filter(d -> d.getCreateTime() != null)
                .filter(d -> {
                    // 将Date转换为LocalDateTime进行比较
                    LocalDateTime createTime = new Timestamp(d.getCreateTime().getTime()).toLocalDateTime();
                    return createTime.isAfter(lastCheckTime);
                })
                .count();

            // 更新最后检查时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            sysConfigService.updateConfigByKey("diagnosis.notification.lastCheckTime", currentTime);

            Map<String, Object> result = new HashMap<>();
            result.put("hasNew", newCount > 0);
            result.put("newCount", newCount);
            result.put("enabled", true);
            result.put("lastCheckTime", currentTime);

            if (newCount > 0) {
                // 获取最新的几条记录用于显示
                List<Diagnosis> newPatients = allPendingList.stream()
                    .filter(d -> d.getCreateTime() != null)
                    .filter(d -> {
                        // 将Date转换为LocalDateTime进行比较
                        LocalDateTime createTime = new Timestamp(d.getCreateTime().getTime()).toLocalDateTime();
                        return createTime.isAfter(lastCheckTime);
                    })
                    .limit(5) // 最多显示5条
                    .collect(Collectors.toList());
                result.put("newPatients", newPatients);
            }

            return success(result);
        } catch (Exception e) {
            logger.error("检查新患者失败", e);
            return error("检查新患者失败: " + e.getMessage());
        }
    }

    /**
     * 获取新患者提醒配置
     */
    @GetMapping("/notification-config")
    public AjaxResult getNotificationConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("enabled", "true".equals(sysConfigService.selectConfigByKey("diagnosis.notification.enabled")));
            config.put("soundEnabled", "true".equals(sysConfigService.selectConfigByKey("diagnosis.notification.sound.enabled")));
            config.put("popupEnabled", "true".equals(sysConfigService.selectConfigByKey("diagnosis.notification.popup.enabled")));
            config.put("checkInterval", sysConfigService.selectConfigByKey("diagnosis.notification.checkInterval"));
            config.put("soundFile", sysConfigService.selectConfigByKey("diagnosis.notification.sound.file"));

            return success(config);
        } catch (Exception e) {
            logger.error("获取提醒配置失败", e);
            return error("获取提醒配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新新患者提醒配置
     */
    @PostMapping("/notification-config")
    public AjaxResult updateNotificationConfig(@RequestBody Map<String, Object> config) {
        try {
            // 更新各项配置
            if (config.containsKey("enabled")) {
                sysConfigService.updateConfigByKey("diagnosis.notification.enabled",
                    String.valueOf(config.get("enabled")));
            }
            if (config.containsKey("soundEnabled")) {
                sysConfigService.updateConfigByKey("diagnosis.notification.sound.enabled",
                    String.valueOf(config.get("soundEnabled")));
            }
            if (config.containsKey("popupEnabled")) {
                sysConfigService.updateConfigByKey("diagnosis.notification.popup.enabled",
                    String.valueOf(config.get("popupEnabled")));
            }
            if (config.containsKey("checkInterval")) {
                sysConfigService.updateConfigByKey("diagnosis.notification.checkInterval",
                    String.valueOf(config.get("checkInterval")));
            }
            if (config.containsKey("soundFile")) {
                sysConfigService.updateConfigByKey("diagnosis.notification.sound.file",
                    String.valueOf(config.get("soundFile")));
            }

            return success("配置更新成功");
        } catch (Exception e) {
            logger.error("更新提醒配置失败", e);
            return error("更新提醒配置失败: " + e.getMessage());
        }
    }
}
