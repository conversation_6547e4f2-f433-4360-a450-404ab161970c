package com.ruoyi.diagnosis.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.diagnosis.domain.DiagnosisRecord;
import com.ruoyi.diagnosis.service.IDiagnosisRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 诊断记录Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/diagnosis/record")
public class DiagnosisRecordController extends BaseController
{
    @Autowired
    private IDiagnosisRecordService diagnosisRecordService;

    /**
     * 查询诊断记录列表
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(DiagnosisRecord diagnosisRecord)
    {
        startPage();
        List<DiagnosisRecord> list = diagnosisRecordService.selectDiagnosisRecordList(diagnosisRecord);
        return getDataTable(list);
    }

    /**
     * 导出诊断记录列表
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:record:export')")
    @Log(title = "诊断记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DiagnosisRecord diagnosisRecord)
    {
        List<DiagnosisRecord> list = diagnosisRecordService.selectDiagnosisRecordList(diagnosisRecord);
        ExcelUtil<DiagnosisRecord> util = new ExcelUtil<DiagnosisRecord>(DiagnosisRecord.class);
        util.exportExcel(response, list, "诊断记录数据");
    }

    /**
     * 获取诊断记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(diagnosisRecordService.selectDiagnosisRecordById(id));
    }

    /**
     * 根据诊断ID查询诊断记录列表
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:record:list')")
    @GetMapping("/listByDiagnosisId/{diagnosisId}")
    public TableDataInfo listByDiagnosisId(@PathVariable("diagnosisId") Long diagnosisId)
    {
        startPage();
        List<DiagnosisRecord> list = diagnosisRecordService.listByDiagnosisId(diagnosisId);
        return getDataTable(list);
    }

    /**
     * 新增诊断记录
     */
    //@PreAuthorize("@ss.hasPermi('diagnosis:record:add')")
    @Log(title = "诊断记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DiagnosisRecord diagnosisRecord)
    {
        return toAjax(diagnosisRecordService.insertDiagnosisRecord(diagnosisRecord));
    }

    /**
     * 修改诊断记录
     */
    //@PreAuthorize("@ss.hasPermi('diagnosis:record:edit')")
    @Log(title = "诊断记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DiagnosisRecord diagnosisRecord)
    {
        return toAjax(diagnosisRecordService.updateDiagnosisRecord(diagnosisRecord));
    }

    /**
     * 删除诊断记录
     */
    @PreAuthorize("@ss.hasPermi('diagnosis:record:remove')")
    @Log(title = "诊断记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(diagnosisRecordService.deleteDiagnosisRecordByIds(ids));
    }
}
