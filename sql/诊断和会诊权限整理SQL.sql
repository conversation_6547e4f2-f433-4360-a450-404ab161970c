-- =============================================
-- 诊断和会诊相关权限插入/更新SQL语句整理
-- 创建时间: 2025-01-06
-- 说明: 整理所有诊断和会诊相关的权限配置，通过权限代码判断是否存在
-- 适用数据库: MySQL 5.7+
-- =============================================

-- =============================================
-- 一、诊断相关权限配置
-- =============================================

-- 1. 诊断管理主菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2000, '诊断管理', 0, 5, 'diagnosis', null, '', '', 1, 0, 'M', '0', '0', '', 'form', 'admin', NOW(), '', NULL, '诊断管理目录'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE menu_name = '诊断管理' AND parent_id = 0);

-- 2. 诊断工作台主菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2001, '诊断工作台', 2000, 1, 'workspace', 'diagnosis/DiagnosisWorkspace', '', '', 1, 0, 'C', '0', '0', 'diagnosis:workspace:list', 'monitor', 'admin', NOW(), '', NULL, '新版影像诊断工作台'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:list');

-- 3. 诊断工作台功能权限按钮
-- 工作台查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2002, '工作台查询', 2001, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:query', '#', 'admin', NOW(), '', NULL, '诊断工作台查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:query');

-- 患者列表权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2003, '患者列表', 2001, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:patient', '#', 'admin', NOW(), '', NULL, '患者列表查看权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:patient');

-- 诊断编辑权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2004, '诊断编辑', 2001, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:edit', '#', 'admin', NOW(), '', NULL, '诊断报告编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:edit');

-- 诊断保存权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2005, '诊断保存', 2001, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:save', '#', 'admin', NOW(), '', NULL, '诊断报告保存权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:save');

-- 诊断提交权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2006, '诊断提交', 2001, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:submit', '#', 'admin', NOW(), '', NULL, '诊断报告提交权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:submit');

-- 诊断审核权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2007, '诊断审核', 2001, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:audit', '#', 'admin', NOW(), '', NULL, '诊断报告审核权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:audit');

-- 模板使用权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2008, '模板使用', 2001, 7, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:template', '#', 'admin', NOW(), '', NULL, '诊断模板使用权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:template');

-- 报告预览权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2009, '报告预览', 2001, 8, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:preview', '#', 'admin', NOW(), '', NULL, '诊断报告预览权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:preview');

-- 报告打印权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2010, '报告打印', 2001, 9, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:print', '#', 'admin', NOW(), '', NULL, '诊断报告打印权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:print');

-- 影像查看权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 2011, '影像查看', 2001, 10, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:workspace:images', '#', 'admin', NOW(), '', NULL, '影像查看权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:workspace:images');

-- =============================================
-- 二、会诊相关权限配置
-- =============================================

-- 1. 会诊管理主菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3000, '会诊管理', 0, 4, 'consultation', '', '', '', 1, 0, 'M', '0', '0', '', 'star', 'admin', NOW(), '', NULL, '会诊管理目录'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE menu_name = '会诊管理' AND parent_id = 0);

-- 2. 我的申请菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3010, '我的申请', 3000, 1, 'my-request', 'consultation/request/index', '', 'MyConsultationRequest', 1, 0, 'C', '0', '0', 'consultation:request:list', 'edit-pen', 'admin', NOW(), '', NULL, '我的会诊申请菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:request:list');

-- 3. 我的申请功能权限
-- 我的申请查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3011, '我的申请查询', 3010, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:request:query', '#', 'admin', NOW(), '', NULL, '我的申请查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:request:query');

-- 发起会诊申请权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3012, '发起会诊申请', 3010, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:request:create', '#', 'admin', NOW(), '', NULL, '发起会诊申请权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:request:create');

-- 修改会诊申请权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3013, '修改会诊申请', 3010, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:request:edit', '#', 'admin', NOW(), '', NULL, '修改会诊申请权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:request:edit');

-- 取消会诊申请权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3014, '取消会诊申请', 3010, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:request:cancel', '#', 'admin', NOW(), '', NULL, '取消会诊申请权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:request:cancel');

-- 删除会诊申请权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3015, '删除会诊申请', 3010, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:request:remove', '#', 'admin', NOW(), '', NULL, '删除会诊申请权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:request:remove');

-- 会诊申请导出权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3016, '会诊申请导出', 3010, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:request:export', '#', 'admin', NOW(), '', NULL, '会诊申请导出权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:request:export');

-- 4. 我的会诊菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3020, '我的会诊', 3000, 2, 'my-consultation', 'consultation/consultant/index', '', 'MyConsultation', 1, 0, 'C', '0', '0', 'consultation:consultant:list', 'user', 'admin', NOW(), '', NULL, '我的会诊任务菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:consultant:list');

-- 5. 我的会诊功能权限
-- 我的会诊查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3021, '我的会诊查询', 3020, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:consultant:query', '#', 'admin', NOW(), '', NULL, '我的会诊查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:consultant:query');

-- 处理会诊申请权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3022, '处理会诊申请', 3020, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:consultant:handle', '#', 'admin', NOW(), '', NULL, '处理会诊申请权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:consultant:handle');

-- 会诊接受权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3023, '会诊接受', 3020, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:consultant:accept', '#', 'admin', NOW(), '', NULL, '会诊接受权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:consultant:accept');

-- 会诊拒绝权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3024, '会诊拒绝', 3020, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:consultant:reject', '#', 'admin', NOW(), '', NULL, '会诊拒绝权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:consultant:reject');

-- 会诊完成权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3025, '会诊完成', 3020, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:consultant:complete', '#', 'admin', NOW(), '', NULL, '会诊完成权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:consultant:complete');

-- 会诊诊断权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3026, '会诊诊断', 3020, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:consultant:diagnose', '#', 'admin', NOW(), '', NULL, '会诊诊断权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:consultant:diagnose');

-- 6. 会诊管理菜单（管理员功能）
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3030, '会诊管理', 3000, 3, 'management', 'consultation/management/index', '', 'ConsultationManagement', 1, 0, 'C', '0', '0', 'consultation:management:list', 'monitor', 'admin', NOW(), '', NULL, '会诊管理菜单（管理员）'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:management:list');

-- 7. 会诊管理功能权限
-- 会诊管理查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3031, '会诊管理查询', 3030, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:management:query', '#', 'admin', NOW(), '', NULL, '会诊管理查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:management:query');

-- 会诊统计查看权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3032, '会诊统计查看', 3030, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:management:statistics', '#', 'admin', NOW(), '', NULL, '会诊统计查看权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:management:statistics');

-- 处理过期申请权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3033, '处理过期申请', 3030, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:management:handleExpired', '#', 'admin', NOW(), '', NULL, '处理过期申请权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:management:handleExpired');

-- 发送会诊提醒权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3034, '发送会诊提醒', 3030, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:management:sendReminders', '#', 'admin', NOW(), '', NULL, '发送会诊提醒权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:management:sendReminders');

-- 会诊分配权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3035, '会诊分配', 3030, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:management:assign', '#', 'admin', NOW(), '', NULL, '会诊分配权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:management:assign');

-- 8. 会诊详情菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3040, '会诊详情', 3000, 4, 'detail', 'consultation/detail/index', '', 'ConsultationDetail', 1, 0, 'C', '0', '0', 'consultation:detail:view', 'view', 'admin', NOW(), '', NULL, '会诊详情查看菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:detail:view');

-- 9. 会诊详情功能权限
-- 会诊详情查看权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3041, '会诊详情查看', 3040, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:detail:view', '#', 'admin', NOW(), '', NULL, '会诊详情查看权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:detail:view');

-- 会诊意见提交权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3042, '会诊意见提交', 3040, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:detail:submit', '#', 'admin', NOW(), '', NULL, '会诊意见提交权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:detail:submit');

-- 会诊影像查看权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3043, '会诊影像查看', 3040, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:detail:viewimage', '#', 'admin', NOW(), '', NULL, '会诊影像查看权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:detail:viewimage');

-- 10. 会诊通知菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3050, '会诊通知', 3000, 5, 'notification', 'consultation/notification/index', '', 'ConsultationNotification', 1, 0, 'C', '0', '0', 'consultation:notification:list', 'message', 'admin', NOW(), '', NULL, '会诊通知管理菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:notification:list');

-- 11. 会诊通知功能权限
-- 通知查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3051, '通知查询', 3050, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:notification:query', '#', 'admin', NOW(), '', NULL, '通知查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:notification:query');

-- 通知标记已读权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3052, '通知标记已读', 3050, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:notification:read', '#', 'admin', NOW(), '', NULL, '通知标记已读权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:notification:read');

-- 通知删除权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 3053, '通知删除', 3050, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'consultation:notification:remove', '#', 'admin', NOW(), '', NULL, '通知删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'consultation:notification:remove');

-- =============================================
-- 三、角色权限分配（可选执行）
-- =============================================

-- 为管理员角色（role_id=1）分配所有诊断权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_id BETWEEN 2000 AND 2011;

-- 为管理员角色（role_id=1）分配所有会诊权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_id BETWEEN 3000 AND 3053;

-- =============================================
-- 四、更新已存在的权限记录（可选）
-- =============================================

-- 更新诊断工作台权限（如果权限已存在但信息不完整）
UPDATE sys_menu SET
    menu_name = '诊断工作台',
    path = 'workspace',
    component = 'diagnosis/DiagnosisWorkspace',
    icon = 'monitor',
    remark = '新版影像诊断工作台',
    update_time = NOW(),
    update_by = 'admin'
WHERE perms = 'diagnosis:workspace:list' AND menu_name != '诊断工作台';

-- 更新会诊管理权限（如果权限已存在但信息不完整）
UPDATE sys_menu SET
    menu_name = '我的申请',
    path = 'my-request',
    component = 'consultation/request/index',
    icon = 'edit-pen',
    remark = '我的会诊申请菜单',
    update_time = NOW(),
    update_by = 'admin'
WHERE perms = 'consultation:request:list' AND menu_name != '我的申请';

-- =============================================
-- 五、权限代码清单（便于检查）
-- =============================================

/*
诊断相关权限代码汇总（11个）：
- diagnosis:workspace:list        # 诊断工作台列表
- diagnosis:workspace:query       # 诊断工作台查询
- diagnosis:workspace:patient     # 患者列表查看
- diagnosis:workspace:edit        # 诊断编辑
- diagnosis:workspace:save        # 诊断保存
- diagnosis:workspace:submit      # 诊断提交
- diagnosis:workspace:audit       # 诊断审核
- diagnosis:workspace:template    # 模板使用
- diagnosis:workspace:preview     # 报告预览
- diagnosis:workspace:print       # 报告打印
- diagnosis:workspace:images      # 影像查看

会诊相关权限代码汇总（33个）：
申请相关：
- consultation:request:list       # 我的申请列表
- consultation:request:query      # 我的申请查询
- consultation:request:create     # 发起会诊申请
- consultation:request:edit       # 修改会诊申请
- consultation:request:cancel     # 取消会诊申请
- consultation:request:remove     # 删除会诊申请
- consultation:request:export     # 会诊申请导出

专家相关：
- consultation:consultant:list    # 我的会诊列表
- consultation:consultant:query   # 我的会诊查询
- consultation:consultant:handle  # 处理会诊申请
- consultation:consultant:accept  # 会诊接受
- consultation:consultant:reject  # 会诊拒绝
- consultation:consultant:complete# 会诊完成
- consultation:consultant:diagnose# 会诊诊断

管理相关：
- consultation:management:list    # 会诊管理列表
- consultation:management:query   # 会诊管理查询
- consultation:management:statistics      # 会诊统计查看
- consultation:management:handleExpired   # 处理过期申请
- consultation:management:sendReminders   # 发送会诊提醒
- consultation:management:assign  # 会诊分配

详情相关：
- consultation:detail:view        # 会诊详情查看
- consultation:detail:submit      # 会诊意见提交
- consultation:detail:viewimage   # 会诊影像查看

通知相关：
- consultation:notification:list  # 会诊通知列表
- consultation:notification:query # 通知查询
- consultation:notification:read  # 通知标记已读
- consultation:notification:remove# 通知删除
*/

-- =============================================
-- 六、执行验证查询
-- =============================================

-- 查询诊断相关权限
SELECT menu_id, menu_name, perms, menu_type, visible, status
FROM sys_menu
WHERE perms LIKE 'diagnosis:%'
ORDER BY menu_id;

-- 查询会诊相关权限
SELECT menu_id, menu_name, perms, menu_type, visible, status
FROM sys_menu
WHERE perms LIKE 'consultation:%'
ORDER BY menu_id;

-- 查询权限分配情况（管理员角色）
SELECT rm.role_id, r.role_name, m.menu_id, m.menu_name, m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key = 'admin'
  AND (m.perms LIKE 'diagnosis:%' OR m.perms LIKE 'consultation:%')
ORDER BY m.menu_id;

-- 查询菜单层级结构
SELECT
    CASE
        WHEN parent_id = 0 THEN '一级菜单'
        WHEN menu_type = 'M' THEN '二级目录'
        WHEN menu_type = 'C' THEN '菜单'
        WHEN menu_type = 'F' THEN '按钮'
    END AS menu_level,
    menu_id,
    menu_name,
    parent_id,
    perms,
    visible,
    status
FROM sys_menu
WHERE menu_id BETWEEN 2000 AND 3053
ORDER BY menu_id;

-- 统计权限数量
SELECT
    '诊断相关权限' AS category,
    COUNT(*) AS count
FROM sys_menu
WHERE perms LIKE 'diagnosis:%'
UNION ALL
SELECT
    '会诊相关权限' AS category,
    COUNT(*) AS count
FROM sys_menu
WHERE perms LIKE 'consultation:%';

-- =============================================
-- 执行说明：
-- 1. 本SQL使用MySQL语法，兼容MySQL 5.7+版本
-- 2. 使用WHERE NOT EXISTS防止重复插入
-- 3. 使用INSERT IGNORE防止角色权限重复分配
-- 4. 所有时间函数使用NOW()替代sysdate()
-- 5. 建议先在测试环境执行验证查询，确认无误后再执行插入语句
-- =============================================
