-- =============================================
-- 诊断模块权限插入SQL语句
-- 基于API权限分析生成
-- 目标用户ID: 1126
-- 创建时间: 2025-08-06
-- 适用数据库: MySQL 5.7+
-- =============================================

-- =============================================
-- 一、诊断管理模块权限菜单
-- =============================================

-- 1. 诊断管理主菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4000, '诊断管理', 0, 5, 'diagnosis', null, '', '', 1, 0, 'M', '0', '0', '', 'form', 'admin', NOW(), '', NULL, '诊断管理目录'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE menu_name = '诊断管理' AND parent_id = 0);

-- 2. 诊断列表菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4001, '诊断列表', 4000, 1, 'list', 'diagnosis/list', '', '', 1, 0, 'C', '0', '0', 'diagnosis:diagnosis:list', 'list', 'admin', NOW(), '', NULL, '诊断列表菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:diagnosis:list');

-- 3. 诊断管理功能权限按钮
-- 诊断查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4002, '诊断查询', 4001, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:diagnosis:query', '#', 'admin', NOW(), '', NULL, '诊断查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:diagnosis:query');

-- 诊断新增权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4003, '诊断新增', 4001, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:diagnosis:add', '#', 'admin', NOW(), '', NULL, '诊断新增权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:diagnosis:add');

-- 诊断编辑权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4004, '诊断编辑', 4001, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:diagnosis:edit', '#', 'admin', NOW(), '', NULL, '诊断编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:diagnosis:edit');

-- 诊断删除权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4005, '诊断删除', 4001, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:diagnosis:remove', '#', 'admin', NOW(), '', NULL, '诊断删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:diagnosis:remove');

-- 诊断审核权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4006, '诊断审核', 4001, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:diagnosis:audit', '#', 'admin', NOW(), '', NULL, '诊断审核权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:diagnosis:audit');

-- =============================================
-- 二、诊断记录模块权限菜单
-- =============================================

-- 1. 诊断记录菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4010, '诊断记录', 4000, 2, 'record', 'diagnosis/record', '', '', 1, 0, 'C', '0', '0', 'diagnosis:record:list', 'documentation', 'admin', NOW(), '', NULL, '诊断记录菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:record:list');

-- 2. 诊断记录功能权限
-- 记录查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4011, '记录查询', 4010, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:record:query', '#', 'admin', NOW(), '', NULL, '诊断记录查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:record:query');

-- 记录新增权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4012, '记录新增', 4010, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:record:add', '#', 'admin', NOW(), '', NULL, '诊断记录新增权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:record:add');

-- 记录编辑权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4013, '记录编辑', 4010, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:record:edit', '#', 'admin', NOW(), '', NULL, '诊断记录编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:record:edit');

-- 记录删除权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4014, '记录删除', 4010, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:record:remove', '#', 'admin', NOW(), '', NULL, '诊断记录删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:record:remove');

-- 记录导出权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4015, '记录导出', 4010, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:record:export', '#', 'admin', NOW(), '', NULL, '诊断记录导出权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:record:export');

-- =============================================
-- 三、诊断字典模块权限菜单
-- =============================================

-- 1. 诊断字典菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4020, '诊断字典', 4000, 3, 'dict', 'diagnosis/dict', '', '', 1, 0, 'C', '0', '0', 'diagnosis:dict:list', 'dict', 'admin', NOW(), '', NULL, '诊断字典菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dict:list');

-- 2. 诊断字典功能权限
-- 字典查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4021, '字典查询', 4020, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dict:query', '#', 'admin', NOW(), '', NULL, '诊断字典查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dict:query');

-- 字典新增权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4022, '字典新增', 4020, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dict:add', '#', 'admin', NOW(), '', NULL, '诊断字典新增权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dict:add');

-- 字典编辑权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4023, '字典编辑', 4020, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dict:edit', '#', 'admin', NOW(), '', NULL, '诊断字典编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dict:edit');

-- 字典删除权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4024, '字典删除', 4020, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dict:remove', '#', 'admin', NOW(), '', NULL, '诊断字典删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dict:remove');

-- 字典导出权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4025, '字典导出', 4020, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dict:export', '#', 'admin', NOW(), '', NULL, '诊断字典导出权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dict:export');

-- =============================================
-- 四、字典分类模块权限菜单
-- =============================================

-- 1. 字典分类菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4030, '字典分类', 4000, 4, 'dictCategory', 'diagnosis/dictCategory', '', '', 1, 0, 'C', '0', '0', 'diagnosis:dictCategory:list', 'tree-table', 'admin', NOW(), '', NULL, '字典分类菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dictCategory:list');

-- 2. 字典分类功能权限
-- 分类查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4031, '分类查询', 4030, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dictCategory:query', '#', 'admin', NOW(), '', NULL, '字典分类查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dictCategory:query');

-- 分类新增权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4032, '分类新增', 4030, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dictCategory:add', '#', 'admin', NOW(), '', NULL, '字典分类新增权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dictCategory:add');

-- 分类编辑权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4033, '分类编辑', 4030, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dictCategory:edit', '#', 'admin', NOW(), '', NULL, '字典分类编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dictCategory:edit');

-- 分类删除权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4034, '分类删除', 4030, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dictCategory:remove', '#', 'admin', NOW(), '', NULL, '字典分类删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dictCategory:remove');

-- 分类导出权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4035, '分类导出', 4030, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:dictCategory:export', '#', 'admin', NOW(), '', NULL, '字典分类导出权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:dictCategory:export');

-- =============================================
-- 五、模板管理模块权限菜单
-- =============================================

-- 1. 模板管理菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4040, '模板管理', 4000, 5, 'template', 'diagnosis/template', '', '', 1, 0, 'C', '0', '0', 'diagnosis:template:list', 'clipboard', 'admin', NOW(), '', NULL, '模板管理菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:template:list');

-- 2. 模板管理功能权限
-- 模板查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4041, '模板查询', 4040, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:template:query', '#', 'admin', NOW(), '', NULL, '模板查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:template:query');

-- 模板新增权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4042, '模板新增', 4040, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:template:add', '#', 'admin', NOW(), '', NULL, '模板新增权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:template:add');

-- 模板编辑权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4043, '模板编辑', 4040, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:template:edit', '#', 'admin', NOW(), '', NULL, '模板编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:template:edit');

-- 模板删除权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4044, '模板删除', 4040, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:template:remove', '#', 'admin', NOW(), '', NULL, '模板删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:template:remove');

-- 模板导出权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4045, '模板导出', 4040, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:template:export', '#', 'admin', NOW(), '', NULL, '模板导出权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:template:export');

-- =============================================
-- 六、报告模板模块权限菜单
-- =============================================

-- 1. 报告模板菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4050, '报告模板', 4000, 6, 'reportTemplate', 'diagnosis/reportTemplate', '', '', 1, 0, 'C', '0', '0', 'diagnosis:reportTemplate:list', 'skill', 'admin', NOW(), '', NULL, '报告模板菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:reportTemplate:list');

-- 2. 报告模板功能权限
-- 报告模板查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4051, '报告模板查询', 4050, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:reportTemplate:query', '#', 'admin', NOW(), '', NULL, '报告模板查询权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:reportTemplate:query');

-- 报告模板新增权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4052, '报告模板新增', 4050, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:reportTemplate:add', '#', 'admin', NOW(), '', NULL, '报告模板新增权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:reportTemplate:add');

-- 报告模板编辑权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4053, '报告模板编辑', 4050, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:reportTemplate:edit', '#', 'admin', NOW(), '', NULL, '报告模板编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:reportTemplate:edit');

-- 报告模板删除权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4054, '报告模板删除', 4050, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:reportTemplate:remove', '#', 'admin', NOW(), '', NULL, '报告模板删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:reportTemplate:remove');

-- 报告模板导出权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4055, '报告模板导出', 4050, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:reportTemplate:export', '#', 'admin', NOW(), '', NULL, '报告模板导出权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:reportTemplate:export');

-- =============================================
-- 七、PDF生成模块权限菜单
-- =============================================

-- 1. PDF管理菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4060, 'PDF管理', 4000, 7, 'pdf', 'diagnosis/pdf', '', '', 1, 0, 'C', '0', '0', 'diagnosis:pdf:generate', 'download', 'admin', NOW(), '', NULL, 'PDF生成管理菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:pdf:generate');

-- 2. PDF配置权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 4061, 'PDF配置', 4060, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'diagnosis:pdf:config', '#', 'admin', NOW(), '', NULL, 'PDF配置管理权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'diagnosis:pdf:config');
